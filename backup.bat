@echo off
echo ========================================
echo COMPONENT FINDER BACKUP SYSTEM
echo ========================================

REM Read current version number
set /p VERSION=<version.txt

echo Current version: %VERSION%
echo Creating backup files with _rev%VERSION% suffix in save directory

echo.
echo Backing up essential files...

REM Core program files
if exist "component_finder.py" copy "component_finder.py" "save\component_finder_rev%VERSION%.py" >nul
if exist "datasheet_finder.py" copy "datasheet_finder.py" "save\datasheet_finder_rev%VERSION%.py" >nul
if exist "digikey_datasheet_improved.py" copy "digikey_datasheet_improved.py" "save\digikey_datasheet_improved_rev%VERSION%.py" >nul
if exist "mouser_datasheet_improved.py" copy "mouser_datasheet_improved.py" "save\mouser_datasheet_improved_rev%VERSION%.py" >nul
if exist "pdf_parser.py" copy "pdf_parser.py" "save\pdf_parser_rev%VERSION%.py" >nul
if exist "rs_components_scraper.py" copy "rs_components_scraper.py" "save\rs_components_scraper_rev%VERSION%.py" >nul
if exist "step_finder.py" copy "step_finder.py" "save\step_finder_rev%VERSION%.py" >nul
if exist "samacsys_credentials.py" copy "samacsys_credentials.py" "save\samacsys_credentials_rev%VERSION%.py" >nul

REM Configuration files
if exist "component_site_credentials.json" copy "component_site_credentials.json" "save\component_site_credentials_rev%VERSION%.json" >nul
if exist "digikey_api_credentials.json" copy "digikey_api_credentials.json" "save\digikey_api_credentials_rev%VERSION%.json" >nul
if exist "samacsys_credentials.json" copy "samacsys_credentials.json" "save\samacsys_credentials_rev%VERSION%.json" >nul
if exist "manufacturer_knowledge.json" copy "manufacturer_knowledge.json" "save\manufacturer_knowledge_rev%VERSION%.json" >nul
if exist "manufacturer_websites.json" copy "manufacturer_websites.json" "save\manufacturer_websites_rev%VERSION%.json" >nul

REM Essential external scripts (already in save directory, just create versioned copies)
if exist "save\ultralibrarian_3d_finder.py" copy "save\ultralibrarian_3d_finder.py" "save\ultralibrarian_3d_finder_rev%VERSION%.py" >nul
if exist "save\samacsys_3d_finder.py" copy "save\samacsys_3d_finder.py" "save\samacsys_3d_finder_rev%VERSION%.py" >nul
if exist "save\snapeda_3d_finder_final.py" copy "save\snapeda_3d_finder_final.py" "save\snapeda_3d_finder_final_rev%VERSION%.py" >nul

REM Batch files
if exist "run_component_finder.bat" copy "run_component_finder.bat" "save\run_component_finder_rev%VERSION%.bat" >nul
if exist "install.bat" copy "install.bat" "save\install_rev%VERSION%.bat" >nul

REM Documentation
if exist "README.md" copy "README.md" "save\README_rev%VERSION%.md" >nul
if exist "COMPONENT_FINDER_WORKFLOW.txt" copy "COMPONENT_FINDER_WORKFLOW.txt" "save\COMPONENT_FINDER_WORKFLOW_rev%VERSION%.txt" >nul

REM Copy version file
if exist "version.txt" copy "version.txt" "save\version_rev%VERSION%.txt" >nul

echo.
echo ✅ Backup created successfully in save directory with _rev%VERSION% suffix
echo.

REM Increment version number for next backup
set /a NEW_VERSION=%VERSION%+1
echo %NEW_VERSION% > version.txt

echo ✅ Version incremented to: %NEW_VERSION%
echo.
echo Backup complete!
pause
