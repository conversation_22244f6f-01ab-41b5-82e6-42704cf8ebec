@echo off
echo ========================================
echo COMPONENT FINDER CLEANUP SYSTEM
echo ========================================
echo.
echo This will delete unneeded files to clean up the directory.
echo.
echo WARNING: This will permanently delete files!
echo.
set /p CONFIRM=Are you sure you want to continue? (y/N): 

if /i not "%CONFIRM%"=="y" (
    echo Cleanup cancelled.
    pause
    exit /b
)

echo.
echo Cleaning up unneeded files...

REM Delete test files
del "automated_test_all_options.py" 2>nul
del "check_code.py" 2>nul
del "check_drawio_installation.py" 2>nul
del "simple_automated_test.py" 2>nul
del "simple_step_test.py" 2>nul
del "test_*.py" 2>nul

REM Delete development/debug files
del "debug_output.txt" 2>nul
del "digikey_token.py" 2>nul
del "download_drawio.py" 2>nul
del "fix_drawio_import.py" 2>nul
del "open_flowchart*.py" 2>nul
del "setup_vscode_mermaid.py" 2>nul

REM Delete DrawIO files
del "*.drawio" 2>nul
del "component_finder_flowchart.mmd" 2>nul
del "draw.io-*.exe" 2>nul
del "drawio_import_guide.txt" 2>nul
del "sample_mermaid_for_drawio.txt" 2>nul

REM Delete RS Components test files
del "rs_components_*.html" 2>nul

REM Delete old batch files
del "backup_version.bat" 2>nul
del "restore_version.bat" 2>nul
del "get_step_*.bat" 2>nul
del "run_step_downloader.bat" 2>nul

REM Delete old Excel files
del "Teledyne_Flir_master-footprint_list*.xlsx" 2>nul

REM Delete old CSV files
del "actual-web-site-xref.csv" 2>nul

REM Delete old README files
del "STEP_DOWNLOADER_README.md" 2>nul

REM Delete Python cache
rmdir /s /q "__pycache__" 2>nul

REM Delete 3d-models directory (old location)
rmdir /s /q "3d-models" 2>nul

REM Delete files-download directory
rmdir /s /q "files-download" 2>nul

REM Delete step_downloads directory
rmdir /s /q "step_downloads" 2>nul

REM Delete python directory if it exists
rmdir /s /q "python" 2>nul

echo.
echo ✅ Cleanup completed!
echo.
echo Remaining essential files:
echo - component_finder.py (main program)
echo - datasheet_finder.py
echo - digikey_datasheet_improved.py
echo - mouser_datasheet_improved.py
echo - Configuration files (*.json)
echo - External 3D scripts (save\*.py)
echo - Help files and documentation
echo - Runtime directories (datasheets\, 3d\)
echo.
pause
