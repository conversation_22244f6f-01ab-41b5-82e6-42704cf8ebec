  1. 📋 Created default manufacturer CSV with 10 entries
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 0 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
 14. 🔍 Starting search for datasheet for Texas Instruments LM358N
 15.    ❌ API found no results for LM358N
 16. 🔍 Searching Digi-Key website for Texas Instruments LM358N data sheet...
 17. ✅ Found Texas Instruments LM358N on Digi-Key
 18.    Part found but no datasheet link on Digi-Key
 19.    🔄 Trying Digi-Key API for better datasheet link...
 20.    ❌ API found no results for LM358N
 21. 🔍 Searching Mouser website for LM358N...
 22.    Opening: https://www.mouser.com/c/?q=LM358N
 23.    Part not found on Mouser
 24. 🌐 Distributors didn't find part, trying Texas Instruments website for lm358n datasheet...
 25. 🆕 Texas Instruments not in knowledge base
 26. 🔍 Searching for Texas Instruments website...
 27. 🔍 Searching Digi-Key for Texas Instruments information...
 28.    Checking: https://www.digikey.com/en/supplier-centers/texas-...
 29. ✅ Found on Digi-Key: Texas Instruments → https://www.facebook.com/texasinstruments
 30. 📋 Added Texas Instruments to CSV
 31. 🌐 Using website: https://www.facebook.com/texasinstruments
 32.    Trying 7 common search URL patterns...
 33.    Trying (1/7): /search?q=PART
 34.    ❌ HTTP 404
 35.    Trying (2/7): /search?query=PART
 36.    ❌ HTTP 404
 37.    Trying (3/7): /search?keyword=PART
 38.    ❌ HTTP 404
 39.    Trying (4/7): /search?term=PART
 40.    ❌ HTTP 404
 41.    Trying (5/7): /search/PART
 42.    ❌ HTTP 404
 43.    Trying (6/7): /products/search?q=PART
 44.    ❌ HTTP 404
 45.    Trying (7/7): /catalog/search?q=PART
 46.    ❌ HTTP 404
 47.    ❌ No working search URLs found
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 1 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
 14. 🔍 Starting search for datasheet for Texas Instruments LM358N
 15. 📡 Trying Digi-Key API for Texas Instruments LM358N...
 16.    📡 Using Digi-Key API for Texas Instruments LM358N...
 17.    ❌ API found no results for LM358N
 18. 🔍 Searching Digi-Key website for Texas Instruments LM358N data sheet...
 19. ✅ Found Texas Instruments LM358N on Digi-Key
 20.    Part found but no datasheet link on Digi-Key
 21.    🔄 Trying Digi-Key API for better datasheet link...
 22. 📡 Trying Digi-Key API for Texas Instruments LM358N...
 23.    📡 Using Digi-Key API for Texas Instruments LM358N...
 24.    ❌ API found no results for LM358N
 25. 🔍 Searching Mouser website for LM358N...
 26.    Opening: https://www.mouser.com/c/?q=LM358N
 27.    Part not found on Mouser
 28. 🌐 Distributors didn't find part, trying Texas Instruments website for lm358n datasheet...
 29. 🆕 Texas Instruments not in knowledge base
 30. 🔍 Searching for Texas Instruments website...
 31. ✅ Found in saved Manufacturer to Website saved : https://www.facebook.com/texasinstruments
 32. 🌐 Using website: https://www.facebook.com/texasinstruments
 33.    Trying 7 common search URL patterns...
 34.    Trying (1/7): /search?q=PART
 35.    ❌ HTTP 404
 36.    Trying (2/7): /search?query=PART
 37.    ❌ HTTP 404
 38.    Trying (3/7): /search?keyword=PART
 39.    ❌ HTTP 404
 40.    Trying (4/7): /search?term=PART
 41.    ❌ HTTP 404
 42.    Trying (5/7): /search/PART
 43.    ❌ HTTP 404
 44.    Trying (6/7): /products/search?q=PART
 45.    ❌ HTTP 404
 46.    Trying (7/7): /catalog/search?q=PART
 47.    ❌ HTTP 404
 48.    ❌ No working search URLs found
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 1 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
 14. 🔍 Starting search for datasheet for Texas Instruments LM358N
 15. 📡 Trying Digi-Key API for Texas Instruments LM358N...
 16.    ❌ Digi-Key API did not find datasheet for LM358N
 17. 🔍 Searching Digi-Key website for Texas Instruments LM358N data sheet...
 18. ✅ Found Texas Instruments LM358N on Digi-Key
 19.    Part found but no datasheet link on Digi-Key
 20.    🔄 Trying Digi-Key API for better datasheet link...
 21. 📡 Trying Digi-Key API for Texas Instruments LM358N...
 22.    ❌ Digi-Key API did not find datasheet for LM358N
 23. 🔍 Searching Mouser website for LM358N...
 24.    Opening: https://www.mouser.com/c/?q=LM358N
 25.    Part not found on Mouser
 26. 🌐 Distributors didn't find part, trying Texas Instruments website for lm358n datasheet...
 27. 🆕 Texas Instruments not in knowledge base
 28. 🔍 Searching for Texas Instruments website...
 29. ✅ Found in saved Manufacturer to Website saved : https://www.facebook.com/texasinstruments
 30. 🌐 Using website: https://www.facebook.com/texasinstruments
 31. 🔍 Searching Texas Instruments datasheet for exact part number LM358N...
 32.    Trying 7 common search URL patterns...
 33.    Trying (1/7): /search?q=PART
 34.    ❌ HTTP 404
 35.    Trying (2/7): /search?query=PART
 36.    ❌ HTTP 404
 37.    Trying (3/7): /search?keyword=PART
 38.    ❌ HTTP 404
 39.    Trying (4/7): /search?term=PART
 40.    ❌ HTTP 404
 41.    Trying (5/7): /search/PART
 42.    ❌ HTTP 404
 43.    Trying (6/7): /products/search?q=PART
 44.    ❌ HTTP 404
 45.    Trying (7/7): /catalog/search?q=PART
 46.    ❌ HTTP 404
 47.    ❌ No working search URLs found
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 1 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
 14. 🔍 Starting search for datasheet for Texas Instruments LM358N
 15. 📡 Trying Digi-Key API for Texas Instruments LM358N...
 16.    ❌ Digi-Key API did not find datasheet for LM358N
 17. 📡 Trying Mouser API for Texas Instruments LM358N...
 18.    ❌ Mouser API did not find datasheet for LM358N
 19. 🔍 Searching Digi-Key website for Texas Instruments LM358N data sheet...
 20. ✅ Found Texas Instruments LM358N on Digi-Key
 21.    Part found but no datasheet link on Digi-Key
 22.    🔄 Trying Digi-Key API for better datasheet link...
 23. 📡 Trying Digi-Key API for Texas Instruments LM358N...
 24.    ❌ Digi-Key API did not find datasheet for LM358N
 25. 🔍 Searching Mouser website for LM358N...
 26.    Opening: https://www.mouser.com/c/?q=LM358N
 27.    Part not found on Mouser
 28. 🌐 Distributors didn't find part, trying Texas Instruments website for lm358n datasheet...
 29. 🆕 Texas Instruments not in knowledge base
 30. 🔍 Searching for Texas Instruments website...
 31. ✅ Found in saved Manufacturer to Website saved : https://www.facebook.com/texasinstruments
 32. 🌐 Using website: https://www.facebook.com/texasinstruments
 33. 🔍 Searching Texas Instruments datasheet for exact part number LM358N...
 34.    Trying 7 common search URL patterns...
 35.    Trying (1/7): /search?q=PART
 36.    ❌ HTTP 404
 37.    Trying (2/7): /search?query=PART
 38.    ❌ HTTP 404
 39.    Trying (3/7): /search?keyword=PART
 40.    ❌ HTTP 404
 41.    Trying (4/7): /search?term=PART
 42.    ❌ HTTP 404
 43.    Trying (5/7): /search/PART
 44.    ❌ HTTP 404
 45.    Trying (6/7): /products/search?q=PART
 46.    ❌ HTTP 404
 47.    Trying (7/7): /catalog/search?q=PART
 48.    ❌ HTTP 404
 49.    ❌ No working search URLs found
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 1 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
 14. 🔍 Starting search for datasheet for Texas Instruments LM358N
 15. 📡 Trying Digi-Key API for Texas Instruments LM358N...
 16.    ❌ Digi-Key API did not find datasheet for LM358N
 17. 📡 Trying Mouser API for Texas Instruments LM358N...
 18.    ❌ Mouser API did not find datasheet for LM358N
 19. 🔍 Normal search on Digi-Key for LM358N...
 20. ✅ Normal search on Digi-Key found datasheet for LM358N
 21.    Datasheet does not contain the part number LM358N
 22. 🔍 Searching Mouser website for LM358N...
 23.    Opening: https://www.mouser.com/c/?q=LM358N
 24.    Part not found on Mouser
 25. 🌐 Distributors didn't find part, trying Texas Instruments website for lm358n datasheet...
 26. 🆕 Texas Instruments not in knowledge base
 27. 🔍 Searching for Texas Instruments website...
 28. ✅ Found in saved Manufacturer to Website saved : https://www.facebook.com/texasinstruments
 29. 🌐 Using website: https://www.facebook.com/texasinstruments
 30. 🔍 Searching Texas Instruments datasheet for exact part number LM358N...
 31.    Trying 7 common search URL patterns...
 32.    Trying (1/7): /search?q=PART
 33.    ❌ HTTP 404
 34.    Trying (2/7): /search?query=PART
 35.    ❌ HTTP 404
 36.    Trying (3/7): /search?keyword=PART
 37.    ❌ HTTP 404
 38.    Trying (4/7): /search?term=PART
 39.    ❌ HTTP 404
 40.    Trying (5/7): /search/PART
 41.    ❌ HTTP 404
 42.    Trying (6/7): /products/search?q=PART
 43.    ❌ HTTP 404
 44.    Trying (7/7): /catalog/search?q=PART
 45.    ❌ HTTP 404
 46.    ❌ No working search URLs found
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 1 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
 14. 🔍 Starting search for datasheet for Texas Instruments LM358N
 15. 📡 Trying Digi-Key API for Texas Instruments LM358N...
 16.    ❌ Digi-Key API did not find datasheet for LM358N
 17. 📡 Trying Mouser API for Texas Instruments LM358N...
 18.    ❌ Mouser API did not find datasheet for LM358N
 19. 🔍 Normal search on Digi-Key for LM358N...
 20. ✅ Normal search on Digi-Key found datasheet for LM358N
 21.    Datasheet does not contain the part number LM358N
 22. 🔍 Normal search on Mouser for LM358N...
 23.    Part not found on Mouser
 24. 🌐 Distributors didn't find part, trying Texas Instruments website for lm358n datasheet...
 25. 🆕 Texas Instruments not in knowledge base
 26. 🔍 Searching for Texas Instruments website...
 27. ✅ Found in saved Manufacturer to Website saved : https://www.facebook.com/texasinstruments
 28. 🌐 Using website: https://www.facebook.com/texasinstruments
 29. 🔍 Searching Texas Instruments datasheet for exact part number LM358N...
 30.    Trying 7 common search URL patterns...
 31.    Trying (1/7): /search?q=PART
 32.    ❌ HTTP 404
 33.    Trying (2/7): /search?query=PART
 34.    ❌ HTTP 404
 35.    Trying (3/7): /search?keyword=PART
 36.    ❌ HTTP 404
 37.    Trying (4/7): /search?term=PART
 38.    ❌ HTTP 404
 39.    Trying (5/7): /search/PART
 40.    ❌ HTTP 404
 41.    Trying (6/7): /products/search?q=PART
 42.    ❌ HTTP 404
 43.    Trying (7/7): /catalog/search?q=PART
 44.    ❌ HTTP 404
 45.    ❌ No working search URLs found
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 1 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
 14. 🔍 Starting search for datasheet for Texas Instruments LM358N
 15. 📡 Trying Digi-Key API for Texas Instruments LM358N...
 16.    ❌ Digi-Key API did not find datasheet for LM358N
 17. 📡 Trying Mouser API for Texas Instruments LM358N...
 18.    ❌ Mouser API did not find datasheet for LM358N
 19. 🔍 Normal search on Digi-Key for LM358N...
 20. ✅ Normal search on Digi-Key found datasheet for LM358N
 21.    Datasheet does not contain the part number LM358N
 22. 🔍 Normal search on Mouser for LM358N...
 23.    Part not found on Mouser
 24. 🌐 Distributors didn't find part, trying Texas Instruments website for lm358n datasheet...
 25. 🆕 Texas Instruments not in knowledge base
 26. 🔍 Searching for Texas Instruments website...
 27. ✅ Found in saved Manufacturer to Website saved : https://www.facebook.com/texasinstruments
 28. 🌐 Using website: https://www.facebook.com/texasinstruments
 29. 🔍 Searching Texas Instruments datasheet for exact part number LM358N...
 30.    Trying 7 common search URL patterns...
 31.    Trying (1/7): /search?q=PART
 32.    ❌ HTTP 404
 33.    Trying (2/7): /search?query=PART
 34.    ❌ HTTP 404
 35.    Trying (3/7): /search?keyword=PART
 36.    ❌ HTTP 404
 37.    Trying (4/7): /search?term=PART
 38.    ❌ HTTP 404
 39.    Trying (5/7): /search/PART
 40.    ❌ HTTP 404
 41.    Trying (6/7): /products/search?q=PART
 42.    ❌ HTTP 404
 43.    Trying (7/7): /catalog/search?q=PART
 44.    ❌ HTTP 404
 45.    ❌ No working search URLs found
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 1 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
 14. 🔍 Starting search for datasheet for Texas Instruments LM358N
 15. 📡 Trying Digi-Key API for Texas Instruments LM358N...
 16.    ❌ Digi-Key API did not find datasheet for LM358N
 17. 📡 Trying Mouser API for Texas Instruments LM358N...
 18.    ❌ Mouser API did not find datasheet for LM358N
 19. 🔍 Normal search on Digi-Key for LM358N...
 20. ✅ Normal search on Digi-Key found datasheet for LM358N
 21.    Datasheet does not contain the part number LM358N
 22. 🔍 Normal search on Mouser for LM358N...
 23.    Part not found on Mouser
 24. 🌐 Distributors didn't find part, trying Texas Instruments website for lm358n datasheet...
 25. 🆕 Texas Instruments not in knowledge base
 26. 🔍 Searching for Texas Instruments website...
 27. ✅ Found in saved Manufacturer to Website saved : https://www.facebook.com/texasinstruments
 28. 🌐 Using website: https://www.facebook.com/texasinstruments
 29. 🔍 Searching Texas Instruments datasheet for exact part number LM358N...
 30.    Trying 7 common search URL patterns...
 31.    Trying (1/7): /search?q=PART
 32.    ❌ HTTP 404
 33.    Trying (2/7): /search?query=PART
 34.    ❌ HTTP 404
 35.    Trying (3/7): /search?keyword=PART
 36.    ❌ HTTP 404
 37.    Trying (4/7): /search?term=PART
 38.    ❌ HTTP 404
 39.    Trying (5/7): /search/PART
 40.    ❌ HTTP 404
 41.    Trying (6/7): /products/search?q=PART
 42.    ❌ HTTP 404
 43.    Trying (7/7): /catalog/search?q=PART
 44.    ❌ HTTP 404
 45.    ❌ No working search URLs found
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 1 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 1 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 1 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 1 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
 14. 🔍 Starting search for datasheet for Texas Instruments LM358N
 15. 📡 Trying Digi-Key API for Texas Instruments LM358N...
 16.    ✅ API found datasheet: https://www.ti.com/general/docs/suppproductinfo.tsp?distId=1...
 17.    ✅ API verified: Texas Instruments LM358N/NOPB
 18. ✅ Manufacturer website found - Texas Instruments
 19. ✅ Distributor verified: Texas Instruments → https://www.ti.com
 20. 📄 Found direct datasheet link: https://www.ti.com/general/docs/suppproductinfo.tsp?distId=10&gotoUrl=https%3A%2F%2Fwww.ti.com%2Flit%2Fgpn%2Flm158-n
 21. 📥 Downloading datasheet from: https://www.ti.com/general/docs/suppproductinfo.ts...
 22. ✅ Downloaded: Texas_Instruments LM358N_datasheet.pdf
 23. 📋 Updated Texas Instruments website in CSV
 24. 🔍 Searching Texas Instruments datasheet for exact part number LM358N...
 25.    Trying 7 common search URL patterns...
 26.    Trying (1/7): /search?q=PART
 27.    ❌ HTTP 404
 28.    Trying (2/7): /search?query=PART
 29.    ❌ HTTP 404
 30.    Trying (3/7): /search?keyword=PART
 31.    ❌ HTTP 404
 32.    Trying (4/7): /search?term=PART
 33.    ❌ HTTP 404
 34.    Trying (5/7): /search/PART
 35.    ❌ HTTP 404
 36.    Trying (6/7): /products/search?q=PART
 37.    ❌ HTTP 404
 38.    Trying (7/7): /catalog/search?q=PART
 39.    ❌ HTTP 404
 40.    ❌ No working search URLs found
 41. ❌ Enhanced 3D finder failed: No STEP file found for Texas Instruments LM358N
 42. 🌐 Distributors didn't find part, trying Texas Instruments website for lm358n datasheet...
 43. 🆕 Texas Instruments not in knowledge base
 44. 🔍 Searching for Texas Instruments website...
 45. ✅ Found in saved Manufacturer to Website saved : https://www.ti.com
 46. 🌐 Using website: https://www.ti.com
 47. 🔍 Searching Texas Instruments datasheet for exact part number LM358N...
 48.    Trying 7 common search URL patterns...
 49.    Trying (1/7): /search?q=PART
 50.    ❌ HTTP 404
 51.    Trying (2/7): /search?query=PART
 52.    ❌ HTTP 404
 53.    Trying (3/7): /search?keyword=PART
 54.    ❌ HTTP 404
 55.    Trying (4/7): /search?term=PART
 56.    ❌ HTTP 404
 57.    Trying (5/7): /search/PART
 58.    ❌ HTTP 404
 59.    Trying (6/7): /products/search?q=PART
 60.    ❌ HTTP 404
 61.    Trying (7/7): /catalog/search?q=PART
 62.    ❌ HTTP 404
 63.    ❌ No working search URLs found
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 1 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 1 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
 14. 🔍 Starting search for datasheet for Texas Instruments LM358N
 15. 📡 Trying Digi-Key API for Texas Instruments LM358N...
 16.    ✅ API found datasheet: https://www.ti.com/general/docs/suppproductinfo.tsp?distId=1...
 17.    ✅ API verified: Texas Instruments LM358N/NOPB
 18. ✅ Manufacturer website found - Texas Instruments
 19. ✅ Distributor verified: Texas Instruments → https://www.ti.com
 20. 📄 Found direct datasheet link: https://www.ti.com/general/docs/suppproductinfo.tsp?distId=10&gotoUrl=https%3A%2F%2Fwww.ti.com%2Flit%2Fgpn%2Flm158-n
 21. 📥 Downloading datasheet from: https://www.ti.com/general/docs/suppproductinfo.ts...
 22. ✅ Downloaded: Texas_Instruments LM358N_datasheet.pdf
 23. 📋 Updated Texas Instruments website in CSV
 24. 🔍 Searching Texas Instruments datasheet for exact part number LM358N...
 25.    Trying 7 common search URL patterns...
 26.    Trying (1/7): /search?q=PART
 27.    ❌ HTTP 404
 28.    Trying (2/7): /search?query=PART
 29.    ❌ HTTP 404
 30.    Trying (3/7): /search?keyword=PART
 31.    ❌ HTTP 404
 32.    Trying (4/7): /search?term=PART
 33.    ❌ HTTP 404
 34.    Trying (5/7): /search/PART
 35.    ❌ HTTP 404
 36.    Trying (6/7): /products/search?q=PART
 37.    ❌ HTTP 404
 38.    Trying (7/7): /catalog/search?q=PART
 39.    ❌ HTTP 404
 40.    ❌ No working search URLs found
 41. ✅ STEP file found - UltraLibrarian
 42. ✅ STEP file found - UltraLibrarian
 43. 🌐 Distributors didn't find part, trying Texas Instruments website for lm358n datasheet...
 44. 🆕 Texas Instruments not in knowledge base
 45. 🔍 Searching for Texas Instruments website...
 46. ✅ Found in saved Manufacturer to Website saved : https://www.ti.com
 47. 🌐 Using website: https://www.ti.com
 48. 🔍 Searching Texas Instruments datasheet for exact part number LM358N...
 49.    Trying 7 common search URL patterns...
 50.    Trying (1/7): /search?q=PART
 51.    ❌ HTTP 404
 52.    Trying (2/7): /search?query=PART
 53.    ❌ HTTP 404
 54.    Trying (3/7): /search?keyword=PART
 55.    ❌ HTTP 404
 56.    Trying (4/7): /search?term=PART
 57.    ❌ HTTP 404
 58.    Trying (5/7): /search/PART
 59.    ❌ HTTP 404
 60.    Trying (6/7): /products/search?q=PART
 61.    ❌ HTTP 404
 62.    Trying (7/7): /catalog/search?q=PART
 63.    ❌ HTTP 404
 64.    ❌ No working search URLs found
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 10 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
 14. 🔍 Starting search for datasheet for Texas Instruments LM358N
 15. 📡 Trying Digi-Key API for Texas Instruments LM358N...
 16.    ✅ API found datasheet: https://www.ti.com/general/docs/suppproductinfo.tsp?distId=1...
 17.    ✅ Datasheet searched LM358N/NOPB is a valid part number.
 18. ✅ Manufacturer website found - Texas Instruments
 19. ✅ Distributor verified: Texas Instruments → https://www.ti.com
 20. 📄 Found direct datasheet link: https://www.ti.com/general/docs/suppproductinfo.tsp?distId=10&gotoUrl=https%3A%2F%2Fwww.ti.com%2Flit%2Fgpn%2Flm158-n
 21. 📥 Downloading datasheet from: https://www.ti.com/general/docs/suppproductinfo.ts...
 22. ✅ Downloaded: Texas_Instruments LM358N_datasheet.pdf
 23. 📋 Updated Texas Instruments website in CSV
 24. 🔍 Searching Texas Instruments datasheet for exact part number LM358N...
 25.    Trying 7 common search URL patterns...
 26.    Trying (1/7): /search?q=PART
 27.    ❌ HTTP 404
 28.    Trying (2/7): /search?query=PART
 29.    ❌ HTTP 404
 30.    Trying (3/7): /search?keyword=PART
 31.    ❌ HTTP 404
 32.    Trying (4/7): /search?term=PART
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 10 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 10 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 10 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
 14. 🔍 Starting search for datasheet for Texas Instruments LM358N
 15. 📡 Trying Digi-Key API for Texas Instruments LM358N...
 16.    ✅ API found datasheet: https://www.ti.com/general/docs/suppproductinfo.tsp?distId=1...
 17.    ✅ Datasheet searched LM358N/NOPB is a valid part number.
 18. ✅ Manufacturer website found - Texas Instruments
 19. ✅ Distributor verified: Texas Instruments → https://www.ti.com
 20. 📄 Found direct datasheet link: https://www.ti.com/general/docs/suppproductinfo.tsp?distId=10&gotoUrl=https%3A%2F%2Fwww.ti.com%2Flit%2Fgpn%2Flm158-n
 21. 📥 Downloading datasheet from: https://www.ti.com/general/docs/suppproductinfo.ts...
 22. ✅ Downloaded: Texas_Instruments LM358N_datasheet.pdf
 23. 📋 Updated Texas Instruments website in CSV
 24. 🔍 Searching Texas Instruments datasheet for exact part number LM358N...
 25.    Trying 7 common search URL patterns...
 26.    Trying (1/7): /search?q=PART
