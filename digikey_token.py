#!/usr/bin/env python3
"""
Digi-Key Token Refresh Utility
This script handles Digi-Key API token refresh
"""

import requests
import json
import sys

def refresh_token():
    """Refresh Digi-Key API token"""
    try:
        # Load current credentials
        with open('digikey_api_credentials.json', 'r') as f:
            creds = json.load(f)
        
        # Token refresh endpoint
        refresh_url = "https://api.digikey.com/v1/oauth2/token"
        
        # Refresh payload
        payload = {
            'client_id': creds['client_id'],
            'client_secret': creds['client_secret'],
            'grant_type': 'client_credentials'
        }
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        print("Refreshing Digi-Key API token...")
        response = requests.post(refresh_url, data=payload, headers=headers, timeout=30)
        
        if response.status_code == 200:
            token_data = response.json()
            
            # Update credentials with new token
            creds['access_token'] = token_data['access_token']
            
            # Save updated credentials
            with open('digikey_api_credentials.json', 'w') as f:
                json.dump(creds, f, indent=2)
            
            print("Token refreshed successfully")
            return True
        else:
            print(f"Token refresh failed: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"Token refresh error: {e}")
        return False

if __name__ == "__main__":
    success = refresh_token()
    sys.exit(0 if success else 1)
