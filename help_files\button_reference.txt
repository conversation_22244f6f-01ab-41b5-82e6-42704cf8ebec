🔘 COMPLETE BUTTON REFERENCE GUIDE

═══════════════════════════════════════════════════════════════

📍 BUTTON LAYOUT:
   The Component Finder has 3 rows of buttons:
   
   ROW 1 (Primary Actions):
   🔍 Search Component | 🗑️ Clear Results | ❓ Help | 🧠 Show Knowledge
   
   ROW 2 (Data Management):
   📋 Edit Websites | 📊 Load Excel File | 🧹 Cleanup Files | 🎓 Test Learning
   
   ROW 3 (Demo/Testing):
   📋 Show All Messages Demo

═══════════════════════════════════════════════════════════════

🔍 SEARCH COMPONENT BUTTON
═══════════════════════════════════════════════════════════════

LOCATION: Row 1, Column 1 (Top-left, primary button)
PURPOSE: Main search function - finds datasheets and 3D models

WHAT IT DOES (API-FIRST APPROACH):
1. Tries Digi-Key API (fastest, most reliable)
2. Tries Mouser API (backup API)
3. Falls back to normal distributor web searches
4. Searches manufacturer website for additional files
5. Searches external 3D sources (UltraLibrarian, SamacSys, SnapEDA)
6. Downloads files automatically with proper naming
7. Validates part numbers in downloaded PDFs
8. Logs all results to CSV file

STEP-BY-STEP USAGE:
1. Enter manufacturer name (e.g., "Texas Instruments")
2. Enter part number (e.g., "LM358N")
3. Select desired search options (checkboxes)
4. Click this button
5. Watch progress in comments area
6. Check status fields for results
7. Find files in datasheets/ and 3d/ folders

═══════════════════════════════════════════════════════════════

🗑️ CLEAR RESULTS BUTTON
═══════════════════════════════════════════════════════════════

LOCATION: Row 1, Column 2
PURPOSE: Cleans up the display for new searches

WHAT IT DOES:
• Clears all text from comments area (bottom text box)
• Resets line numbering to start fresh
• Does NOT clear input fields (manufacturer/part number)
• Does NOT clear status fields (Status/Datasheet/3D Model)
• Does NOT delete any downloaded files
• Does NOT affect knowledge base or learned patterns

WHEN TO USE:
• Before starting a new search for cleaner display
• When comments area becomes too cluttered
• To reset display without losing your input data

═══════════════════════════════════════════════════════════════

❓ HELP BUTTON
═══════════════════════════════════════════════════════════════

LOCATION: Row 1, Column 3
PURPOSE: Shows comprehensive help system

WHAT IT OPENS:
• Quick Start Guide - Basic usage instructions
• Button Guide - Detailed explanation of all buttons
• Learning System - How the AI learning works
• Excel Setup - Batch processing instructions
• Troubleshooting - Common problems and solutions
• About - Version history and features

HELP TABS EXPLAINED:
• 🚀 Quick Start - Get started immediately
• 🔘 Button Guide - This detailed button reference
• 🎓 Learning System - Interactive learning features
• 📊 Excel Setup - Batch processing setup
• 🔧 Troubleshooting - Problem solving guide
• ℹ️ About - Program information and history

═══════════════════════════════════════════════════════════════

🧠 SHOW KNOWLEDGE BUTTON
═══════════════════════════════════════════════════════════════

LOCATION: Row 1, Column 4
PURPOSE: Display learned manufacturer patterns and knowledge base

WHAT IT SHOWS:
• Known manufacturers and their websites
• Learned download patterns for each manufacturer
• Success rates and statistics
• Known parts database
• Search patterns that work

KNOWLEDGE BASE INCLUDES:
• Manufacturer website URLs
• Download link patterns
• Search form locations
• File naming conventions
• Part number variations
• Success/failure statistics

WHEN TO USE:
• To see what manufacturers the system knows
• To verify learned patterns are correct
• To understand why searches succeed or fail
• To check if a manufacturer is in the database

═══════════════════════════════════════════════════════════════

📋 EDIT WEBSITES BUTTON
═══════════════════════════════════════════════════════════════

LOCATION: Row 2, Column 1
PURPOSE: Edit the manufacturer website database

WHAT IT OPENS:
• CSV file with manufacturer-to-website mappings
• Opens in your default spreadsheet program (Excel, etc.)
• Shows current manufacturer database

CSV FILE FORMAT:
• Column 1: Manufacturer name (exact spelling)
• Column 2: Website URL (full URL with https://)
• Example: "Texas Instruments,https://www.ti.com"

WHEN TO USE:
• Add new manufacturers not in database
• Correct wrong website URLs
• Add manufacturer name variations
• Fix spelling errors in manufacturer names

IMPORTANT NOTES:
• Save the file after editing
• Use exact manufacturer names from distributor sites
• Include full URLs with https://
• Changes take effect immediately

═══════════════════════════════════════════════════════════════

📊 LOAD EXCEL FILE BUTTON
═══════════════════════════════════════════════════════════════

LOCATION: Row 2, Column 2
PURPOSE: Process multiple components from Excel spreadsheet

WHAT IT DOES:
• Opens file dialog to select Excel file
• Reads component list from spreadsheet
• Processes each component automatically
• Updates spreadsheet with results
• Validates part numbers in downloaded PDFs
• Shows progress for each component

EXCEL FILE REQUIREMENTS:
• Must have "Manufacturer" column
• Must have "Part Number" column
• Can have additional columns (preserved)
• Supports .xlsx and .xls formats

PROCESSING FEATURES:
• Automatic progress tracking
• Resume capability if interrupted
• Part number validation in PDFs
• User prompts for validation failures
• Detailed logging of all results

WHEN TO USE:
• Processing large lists of components
• Batch downloading datasheets and 3D models
• Automated component research
• Building component libraries

═══════════════════════════════════════════════════════════════

🧹 CLEANUP FILES BUTTON
═══════════════════════════════════════════════════════════════

LOCATION: Row 2, Column 3
PURPOSE: Remove temporary, duplicate, and debug files

WHAT IT CLEANS:
• Temporary download files
• Duplicate datasheets and 3D models
• Debug output files
• Empty folders
• Corrupted or incomplete downloads
• Old log files (keeps recent ones)

SAFETY FEATURES:
• Shows preview of files to be deleted
• Asks for confirmation before deletion
• Never deletes valid datasheets or 3D models
• Preserves important configuration files
• Creates backup before major cleanup

WHEN TO USE:
• After large batch processing sessions
• When disk space is running low
• To remove failed download attempts
• Regular maintenance (weekly/monthly)

═══════════════════════════════════════════════════════════════

🎓 TEST LEARNING BUTTON
═══════════════════════════════════════════════════════════════

LOCATION: Row 2, Column 4
PURPOSE: Test the interactive learning system

WHAT IT DOES:
• Simulates learning scenarios
• Tests pattern recognition
• Validates knowledge base integrity
• Shows learning system capabilities
• Demonstrates manual intervention process

TEST SCENARIOS:
• Unknown manufacturer website detection
• Download pattern learning
• Manual file location assistance
• Pattern validation and storage
• Success rate calculation

WHEN TO USE:
• Understanding how learning works
• Troubleshooting learning issues
• Demonstrating system capabilities
• Validating knowledge base accuracy

═══════════════════════════════════════════════════════════════

📋 SHOW ALL MESSAGES DEMO BUTTON
═══════════════════════════════════════════════════════════════

LOCATION: Row 3, Column 1
PURPOSE: Display all possible search messages for testing

WHAT IT SHOWS:
• All success messages
• All error messages
• All progress indicators
• All validation prompts
• All learning system messages

DEMO CATEGORIES:
• API search messages
• Web scraping messages
• Download progress messages
• Validation messages
• Error and warning messages
• Learning system interactions

WHEN TO USE:
• Understanding system feedback
• Testing message display
• Troubleshooting communication issues
• Learning what different messages mean

═══════════════════════════════════════════════════════════════
