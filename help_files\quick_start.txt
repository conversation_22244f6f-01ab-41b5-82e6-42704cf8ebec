🚀 COMPONENT FINDER - QUICK START GUIDE

═══════════════════════════════════════════════════════════════

🎯 MAIN PURPOSE:
   Automatically find and download datasheets and 3D STEP files
   for electronic components using APIs and web scraping.

🔍 SINGLE PART SEARCH:
1. Enter Manufacturer (e.g., "Texas Instruments")
2. Enter Part Number (e.g., "LM358N")
3. Click "🔍 Search Component"
4. System searches: Digi-Key API → Mouser API → Web scraping

📊 EXCEL BATCH SEARCH:
1. Click "📊 Load Excel File"
2. Select your Excel file with components
3. System processes all parts automatically with validation

🔘 ALL BUTTONS EXPLAINED:

ROW 1 - PRIMARY ACTIONS:
• 🔍 Search Component - Start API-first search for single component
• 🗑️ Clear Results - Clear all results, comments, and status fields
• ❓ Help - Show this comprehensive help system
• 🧠 Show Knowledge - View learned manufacturer patterns database

ROW 2 - DATA MANAGEMENT:
• 📋 Edit Websites - Edit manufacturer website database (CSV)
• 📊 Load Excel File - Process multiple components from spreadsheet
• 🧹 Cleanup Files - Remove temporary, duplicate, and debug files
• 🎓 Test Learning - Test interactive learning system

ROW 3 - DEMO/TESTING:
• 📋 Show All Messages Demo - Display all possible search messages

🎓 LEARNING SYSTEM:
   When files can't be found automatically:
   • Opens webpage in browser for manual search
   • Learns download patterns for future automation
   • Saves patterns in manufacturer_knowledge.json

📁 FILE ORGANIZATION:
   • Datasheets: datasheets/ folder (PDF files)
   • STEP files: 3d/ folder (3D models)
   • Search logs: found-files-log.csv
   • Knowledge: manufacturer_knowledge.json

🔄 SEARCH PRIORITY ORDER:
1. Digi-Key API (fastest, most reliable)
2. Mouser API (backup API)
3. Normal distributor web searches
4. Manufacturer website search
5. External 3D sources (UltraLibrarian, SamacSys, SnapEDA)

💡 TIPS:
   • APIs work best - enable Digi-Key and Mouser checkboxes
   • Use exact manufacturer names from distributor websites
   • System validates part numbers in downloaded PDFs
   • Check comments area for detailed progress

═══════════════════════════════════════════════════════════════

🆘 NEED HELP?
   • Click other help tabs for detailed button explanations
   • Try example: "Texas Instruments" + "LM358N"
   • Check troubleshooting tab if APIs fail

═══════════════════════════════════════════════════════════════