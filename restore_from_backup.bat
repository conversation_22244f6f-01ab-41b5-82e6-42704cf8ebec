@echo off
echo ========================================
echo COMPONENT FINDER RESTORE SYSTEM
echo ========================================
echo.

REM List available backup versions
echo Available backup versions in save directory:
for %%i in (save\*_rev*.py) do echo - %%~ni
for %%i in (save\*_rev*.json) do echo - %%~ni
for %%i in (save\*_rev*.bat) do echo - %%~ni

echo.
set /p VERSION=Enter version number to restore from (e.g., 6):

REM Check if backup files exist for this version
if not exist "save\component_finder_rev%VERSION%.py" (
    echo Error: No backup files found for version %VERSION%!
    pause
    exit /b
)

echo.
echo WARNING: This will overwrite current files with backup files from version %VERSION%!
echo.
set /p CONFIRM=Are you sure you want to restore from version %VERSION%? (y/N):

if /i not "%CONFIRM%"=="y" (
    echo Restore cancelled.
    pause
    exit /b
)

echo.
echo Restoring from backup version: %VERSION%

REM Restore core files
if exist "save\component_finder_rev%VERSION%.py" copy "save\component_finder_rev%VERSION%.py" "component_finder.py" >nul
if exist "save\datasheet_finder_rev%VERSION%.py" copy "save\datasheet_finder_rev%VERSION%.py" "datasheet_finder.py" >nul
if exist "save\digikey_datasheet_improved_rev%VERSION%.py" copy "save\digikey_datasheet_improved_rev%VERSION%.py" "digikey_datasheet_improved.py" >nul
if exist "save\mouser_datasheet_improved_rev%VERSION%.py" copy "save\mouser_datasheet_improved_rev%VERSION%.py" "mouser_datasheet_improved.py" >nul
if exist "save\pdf_parser_rev%VERSION%.py" copy "save\pdf_parser_rev%VERSION%.py" "pdf_parser.py" >nul
if exist "save\rs_components_scraper_rev%VERSION%.py" copy "save\rs_components_scraper_rev%VERSION%.py" "rs_components_scraper.py" >nul
if exist "save\step_finder_rev%VERSION%.py" copy "save\step_finder_rev%VERSION%.py" "step_finder.py" >nul
if exist "save\samacsys_credentials_rev%VERSION%.py" copy "save\samacsys_credentials_rev%VERSION%.py" "samacsys_credentials.py" >nul

REM Restore configuration files
if exist "save\component_site_credentials_rev%VERSION%.json" copy "save\component_site_credentials_rev%VERSION%.json" "component_site_credentials.json" >nul
if exist "save\digikey_api_credentials_rev%VERSION%.json" copy "save\digikey_api_credentials_rev%VERSION%.json" "digikey_api_credentials.json" >nul
if exist "save\samacsys_credentials_rev%VERSION%.json" copy "save\samacsys_credentials_rev%VERSION%.json" "samacsys_credentials.json" >nul
if exist "save\manufacturer_knowledge_rev%VERSION%.json" copy "save\manufacturer_knowledge_rev%VERSION%.json" "manufacturer_knowledge.json" >nul
if exist "save\manufacturer_websites_rev%VERSION%.json" copy "save\manufacturer_websites_rev%VERSION%.json" "manufacturer_websites.json" >nul

REM Restore external scripts
if exist "save\ultralibrarian_3d_finder_rev%VERSION%.py" copy "save\ultralibrarian_3d_finder_rev%VERSION%.py" "save\ultralibrarian_3d_finder.py" >nul
if exist "save\samacsys_3d_finder_rev%VERSION%.py" copy "save\samacsys_3d_finder_rev%VERSION%.py" "save\samacsys_3d_finder.py" >nul
if exist "save\snapeda_3d_finder_final_rev%VERSION%.py" copy "save\snapeda_3d_finder_final_rev%VERSION%.py" "save\snapeda_3d_finder_final.py" >nul

REM Restore batch files
if exist "save\run_component_finder_rev%VERSION%.bat" copy "save\run_component_finder_rev%VERSION%.bat" "run_component_finder.bat" >nul
if exist "save\install_rev%VERSION%.bat" copy "save\install_rev%VERSION%.bat" "install.bat" >nul

REM Restore documentation
if exist "save\README_rev%VERSION%.md" copy "save\README_rev%VERSION%.md" "README.md" >nul
if exist "save\COMPONENT_FINDER_WORKFLOW_rev%VERSION%.txt" copy "save\COMPONENT_FINDER_WORKFLOW_rev%VERSION%.txt" "COMPONENT_FINDER_WORKFLOW.txt" >nul

REM Restore version file
if exist "save\version_rev%VERSION%.txt" copy "save\version_rev%VERSION%.txt" "version.txt" >nul

echo.
echo ✅ Restore completed successfully!
echo.
echo Restored from version: %VERSION%
pause
