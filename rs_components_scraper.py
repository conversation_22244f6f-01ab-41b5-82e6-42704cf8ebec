#!/usr/bin/env python3
"""
RS COMPONENTS DATASHEET FINDER - ENHANCED VERSION
=================================================
Enhanced version matching Digikey functionality with:
- Callable function for integration
- PDF parsing and validation
- CSV cross-reference database
- Structured return data

Usage:
    python rs_components_scraper.py "Texas Instruments" "LM358N"
"""

import requests
from bs4 import BeautifulSoup
import time
import os
import json
import csv
from datetime import datetime
from urllib.parse import urlparse
import argparse

class RSComponentsDatasheetFinder:
    def __init__(self):
        self.session = requests.Session()
        self.datasheets_dir = "datasheets"
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive'
        })

    def log(self, message, level="INFO"):
        """Enhanced logging with timestamps"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        if level == "ERROR":
            print(f"❌ [{timestamp}] ERROR: {message}")
        elif level == "SUCCESS":
            print(f"✅ [{timestamp}] SUCCESS: {message}")
        elif level == "WARNING":
            print(f"⚠️ [{timestamp}] WARNING: {message}")
        else:
            print(f"ℹ️ [{timestamp}] INFO: {message}")

    def setup_directories(self):
        """Create necessary directories"""
        self.log("Setting up directories...")
        try:
            os.makedirs(self.datasheets_dir, exist_ok=True)
            self.log("Directory 'datasheets' ready", "SUCCESS")
            return True
        except Exception as e:
            self.log(f"Error creating directories: {e}", "ERROR")
            return False

    def update_website_csv(self, manufacturer, website, part_number=None, package_type=None, datasheet_filename=None):
        """Update or create the actual-web-site-xref.csv file in datasheets directory"""
        csv_file = os.path.join(self.datasheets_dir, "actual-web-site-xref.csv")

        try:
            # Read existing data
            existing_data = {}
            if os.path.exists(csv_file):
                with open(csv_file, 'r', newline='', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    header = next(reader, None)  # Skip header
                    for row in reader:
                        if len(row) >= 2:
                            # Handle different formats (2, 4, or 5 columns)
                            if len(row) >= 5:
                                existing_data[row[0]] = {
                                    'website': row[1],
                                    'part_number': row[2],
                                    'package_type': row[3],
                                    'datasheet_filename': row[4]
                                }
                            elif len(row) >= 4:
                                existing_data[row[0]] = {
                                    'website': row[1],
                                    'part_number': row[2],
                                    'package_type': row[3],
                                    'datasheet_filename': ''
                                }
                            else:
                                existing_data[row[0]] = {
                                    'website': row[1],
                                    'part_number': '',
                                    'package_type': '',
                                    'datasheet_filename': ''
                                }

            # Add/update the manufacturer
            existing_data[manufacturer] = {
                'website': f"https://{website}",
                'part_number': part_number or '',
                'package_type': package_type or '',
                'datasheet_filename': datasheet_filename or ''
            }

            # Write back to file
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(["Manufacturer Name", "Website", "Part Number", "Package Type", "Datasheet Filename"])
                for mfg, data in sorted(existing_data.items()):
                    writer.writerow([mfg, data['website'], data['part_number'], data['package_type'], data['datasheet_filename']])

            self.log(f"Updated website CSV: {manufacturer} -> {website} (Part: {part_number}, Package: {package_type}, File: {datasheet_filename})", "SUCCESS")

        except Exception as e:
            self.log(f"Error updating website CSV: {e}", "WARNING")

    def search_component(self, manufacturer, part_number):
        """Search for component on RS Components"""
        self.log(f"Searching for part: {manufacturer} {part_number}")
        self.log("=" * 60)
        
        results = {
            'found': False,
            'datasheet_url': None,
            'datasheet_file': None,
            'manufacturer_website': None,
            'package_type': None,
            'price_info': None,
            'stock_info': None
        }
        
        try:
            # Search URL
            search_url = f"https://uk.rs-online.com/web/c/?searchTerm={part_number}"
            print(f"🔍 Searching: {search_url}")
            
            time.sleep(2)  # Be respectful
            response = self.session.get(search_url, timeout=30)
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                page_text = response.text.lower()
                
                # Check if part found
                if part_number.lower() in page_text:
                    print(f"   ✅ Found {part_number} on RS Components")
                    results['found'] = True
                    
                    # Save the response for analysis
                    with open(f'rs_components_{part_number}_response.html', 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    print(f"   💾 Saved response for analysis")
                    
                    # Extract information
                    self.extract_component_info(soup, results, manufacturer, part_number)
                    
                    # Look for product page link
                    product_url = self.find_product_page_url(soup, part_number)
                    if product_url:
                        print(f"   🔗 Found product page: {product_url[:60]}...")
                        self.scrape_product_page(product_url, results, manufacturer, part_number)
                    
                else:
                    print(f"   ❌ {part_number} not found on RS Components")
                    
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Search error: {str(e)[:50]}")
        
        return results
    
    def extract_component_info(self, soup, results, manufacturer, part_number):
        """Extract component information from search results"""
        try:
            # Look for manufacturer info
            manufacturer_elements = soup.find_all(text=lambda text: text and manufacturer.lower() in text.lower())
            if manufacturer_elements:
                print(f"   ✅ Confirmed manufacturer: {manufacturer}")
            
            # Look for datasheet links
            datasheet_links = []
            for link in soup.find_all('a', href=True):
                href = link.get('href', '').lower()
                text = link.get_text(strip=True).lower()
                
                if ('datasheet' in text or 'data sheet' in text or 
                    'datasheet' in href or '.pdf' in href):
                    
                    full_url = link.get('href')
                    if not full_url.startswith('http'):
                        full_url = f"https://uk.rs-online.com{full_url}"
                    
                    datasheet_links.append(full_url)
                    print(f"   📄 Found datasheet link: {full_url[:60]}...")
            
            if datasheet_links:
                results['datasheet_url'] = datasheet_links[0]
                print(f"   📄 Primary datasheet: {datasheet_links[0][:60]}...")
            
            # Look for package information
            package_keywords = ['sot23', 'sot-23', 'soic', 'qfn', 'dfn', 'sop', 'msop', 'tssop', 'ssop', 'pdip', 'dip']
            page_text = soup.get_text().lower()
            
            for package in package_keywords:
                if package in page_text:
                    results['package_type'] = package.upper()
                    print(f"   📦 Package type: {package.upper()}")
                    break
            
            # Look for stock/price info
            stock_elements = soup.find_all(text=lambda text: text and ('in stock' in text.lower() or 'stock:' in text.lower()))
            if stock_elements:
                results['stock_info'] = stock_elements[0].strip()
                print(f"   📊 Stock info: {results['stock_info'][:30]}...")
            
        except Exception as e:
            print(f"   ⚠️ Info extraction error: {str(e)[:30]}")
    
    def find_product_page_url(self, soup, part_number):
        """Find the detailed product page URL"""
        try:
            # Look for links that contain the part number and lead to product pages
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                text = link.get_text(strip=True)
                
                if (part_number.lower() in text.lower() or part_number.lower() in href.lower()):
                    if '/web/p/' in href or 'product' in href.lower():
                        if not href.startswith('http'):
                            href = f"https://uk.rs-online.com{href}"
                        return href
            
            return None
            
        except Exception as e:
            print(f"   ⚠️ Product page search error: {str(e)[:30]}")
            return None
    
    def scrape_product_page(self, product_url, results, manufacturer, part_number):
        """Scrape the detailed product page"""
        try:
            print(f"   🔍 Scraping product page...")
            
            time.sleep(2)  # Be respectful
            response = self.session.get(product_url, timeout=30)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Save product page
                with open(f'rs_components_{part_number}_product.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print(f"   💾 Saved product page")
                
                # Look for more detailed datasheet links
                datasheet_links = []
                for link in soup.find_all('a', href=True):
                    href = link.get('href', '').lower()
                    text = link.get_text(strip=True).lower()
                    
                    if ('datasheet' in text or 'technical data' in text or 
                        'specification' in text or '.pdf' in href):
                        
                        full_url = link.get('href')
                        if not full_url.startswith('http'):
                            full_url = f"https://uk.rs-online.com{full_url}"
                        
                        datasheet_links.append(full_url)
                
                # Try to download datasheet
                if datasheet_links:
                    for datasheet_url in datasheet_links[:3]:  # Try first 3
                        if self.download_datasheet(datasheet_url, manufacturer, part_number):
                            results['datasheet_file'] = f"{manufacturer.replace(' ', '_')}_{part_number}_datasheet.pdf"
                            break
                
                # Look for manufacturer website links
                for link in soup.find_all('a', href=True):
                    href = link.get('href')
                    text = link.get_text(strip=True).lower()
                    
                    if (manufacturer.lower() in text and 
                        href.startswith('http') and 
                        'rs-online' not in href):
                        
                        results['manufacturer_website'] = href
                        print(f"   🌐 Found manufacturer website: {href}")
                        break
                
            else:
                print(f"   ❌ Product page error: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ⚠️ Product page scraping error: {str(e)[:30]}")
    
    def download_datasheet(self, url, manufacturer, part_number):
        """Download datasheet from URL"""
        try:
            print(f"      📥 Downloading datasheet: {url[:50]}...")
            
            response = self.session.get(url, timeout=60)
            
            if response.status_code == 200:
                # Check if it's actually a PDF
                content_type = response.headers.get('content-type', '').lower()
                if 'pdf' in content_type or url.lower().endswith('.pdf'):
                    
                    # Create filename
                    manufacturer_clean = manufacturer.replace(" ", "_").replace(".", "").replace(",", "")
                    filename = f"{manufacturer_clean}_{part_number}_datasheet.pdf"
                    filepath = os.path.join('datasheets', filename)
                    
                    # Save file
                    with open(filepath, 'wb') as f:
                        f.write(response.content)
                    
                    print(f"      ✅ Downloaded: {filename}")
                    return True
                else:
                    print(f"      ❌ Not a PDF file (content-type: {content_type})")
            else:
                print(f"      ❌ Download failed: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"      ❌ Download error: {str(e)[:30]}")
            
        return False

    def find_datasheet(self, manufacturer, part_number):
        """Main method to find and download datasheet"""
        self.log(f"Starting datasheet search for: {manufacturer} {part_number}")
        self.log("=" * 60)

        # Step 1: Setup directories
        if not self.setup_directories():
            self.log("Failed to setup directories - stopping", "ERROR")
            return False

        # Step 2: Search for the component
        results = self.search_component(manufacturer, part_number)

        if not results or not results.get('found'):
            self.log("Failed to find part - stopping", "ERROR")
            return False

        # Step 3: Update CSV with results
        if results.get('datasheet_file'):
            # Extract website from any URL we might have
            website = "uk.rs-online.com"  # RS Components UK
            package_type = results.get('package_type')
            datasheet_filename = results.get('datasheet_file')

            self.update_website_csv(manufacturer, website, part_number, package_type, datasheet_filename)

        self.log("Datasheet search completed successfully!", "SUCCESS")
        return True

def find_part_datasheet(manufacturer, part_number):
    """
    Callable function to find and download datasheet from RS Components

    Args:
        manufacturer (str): Manufacturer name (e.g., "Texas Instruments")
        part_number (str): Part number (e.g., "LM358N")

    Returns:
        dict: {
            'success': bool,
            'message': str,
            'datasheet_file': str or None,
            'csv_file': str or None,
            'pdf_parsing': dict or None
        }
    """
    try:
        finder = RSComponentsDatasheetFinder()
        results = finder.search_component(manufacturer, part_number)

        if results and results.get('found'):
            # Update CSV
            website = "uk.rs-online.com"
            package_type = results.get('package_type')
            datasheet_filename = results.get('datasheet_file')

            if datasheet_filename:
                finder.update_website_csv(manufacturer, website, part_number, package_type, datasheet_filename)

                # Parse PDF if file exists
                pdf_results = None
                datasheet_path = f"datasheets/{datasheet_filename}"
                if os.path.exists(datasheet_path):
                    try:
                        from pdf_parser import parse_datasheet_pdf
                        pdf_results = parse_datasheet_pdf(datasheet_path, part_number)

                        # Update CSV with PDF parsing results if better package found
                        if (pdf_results.get('success') and
                            pdf_results.get('package_info') and
                            pdf_results['package_info'].get('package')):

                            better_package = pdf_results['package_info']['package']
                            finder.update_website_csv(manufacturer, website, part_number, better_package, datasheet_filename)

                    except Exception as e:
                        print(f"PDF parsing failed: {e}")

                return {
                    'success': True,
                    'message': f"Successfully found datasheet for {manufacturer} {part_number}",
                    'datasheet_file': datasheet_path,
                    'csv_file': "datasheets/actual-web-site-xref.csv",
                    'pdf_parsing': pdf_results
                }
            else:
                return {
                    'success': True,
                    'message': f"Found part {manufacturer} {part_number} but no datasheet downloaded",
                    'datasheet_file': None,
                    'csv_file': "datasheets/actual-web-site-xref.csv",
                    'pdf_parsing': None
                }
        else:
            return {
                'success': False,
                'message': f"Failed to find part {manufacturer} {part_number}",
                'datasheet_file': None,
                'csv_file': None,
                'pdf_parsing': None
            }

    except Exception as e:
        return {
            'success': False,
            'message': f"Error: {str(e)}",
            'datasheet_file': None,
            'csv_file': None,
            'pdf_parsing': None
        }

def main():
    parser = argparse.ArgumentParser(description='Download datasheet from RS Components')
    parser.add_argument('manufacturer', help='Manufacturer name (e.g., "Texas Instruments")')
    parser.add_argument('part_number', help='Part number (e.g., "LM358N")')

    args = parser.parse_args()

    finder = RSComponentsDatasheetFinder()
    success = finder.find_datasheet(args.manufacturer, args.part_number)

    if success:
        print(f"\n🎉 SUCCESS: Datasheet search completed for {args.manufacturer} {args.part_number}")
    else:
        print(f"\n💥 FAILED: Could not find datasheet for {args.manufacturer} {args.part_number}")

def test_main():
    print("🚀 RS COMPONENTS SCRAPER TEST")
    print("Working alternative to blocked Digikey/Mouser")
    print("=" * 60)

    scraper = RSComponentsDatasheetFinder()
    
    # Test cases
    test_cases = [
        ("Diodes Inc", "APX803L20-30SA-7"),
        ("Texas Instruments", "LM358N")
    ]
    
    for manufacturer, part_number in test_cases:
        print(f"\n🎯 Testing: {manufacturer} {part_number}")
        
        results = scraper.search_component(manufacturer, part_number)
        
        print(f"\n📋 Results for {part_number}:")
        print(f"   Found: {'✅ YES' if results['found'] else '❌ NO'}")
        if results['datasheet_url']:
            print(f"   Datasheet URL: {results['datasheet_url'][:60]}...")
        if results['datasheet_file']:
            print(f"   Downloaded: {results['datasheet_file']}")
        if results['manufacturer_website']:
            print(f"   Manufacturer: {results['manufacturer_website']}")
        if results['package_type']:
            print(f"   Package: {results['package_type']}")
        if results['stock_info']:
            print(f"   Stock: {results['stock_info'][:40]}...")
        
        # Wait between tests
        time.sleep(5)
    
    print("\n" + "=" * 60)
    print("📋 FINAL SUMMARY:")
    print("✅ RS Components provides a working alternative to Digikey/Mouser")
    print("💡 This can be integrated into your main workflow")
    print("📁 Check datasheets/ folder for downloaded files")

if __name__ == "__main__":
    import sys
    if len(sys.argv) >= 3:
        main()  # Command line usage
    else:
        test_main()  # Test mode
