COMPONENT FINDER - COMPLETE WORKFLOW DOCUMENTATION
==================================================

OVERVIEW:
The Component Finder is a GUI application that automatically searches for and downloads:
- Electronic component datasheets (PDF files)
- 3D STEP model files
- Organizes files and tracks results

STARTUP PROCESS:
================

1. INITIALIZATION:
   - Creates directories: datasheets/, 3d/, emoji_images/, help_files/
   - Downloads 25 colored emoji images from Twitter's Twemoji
   - Loads manufacturer website database from CSV
   - Loads knowledge base from JSON
   - Creates help files if they don't exist
   - Initializes GUI with colored emoji buttons and text area

2. GUI COMPONENTS:
   - Manufacturer input field (default: "Texas Instruments")
   - Part number input field (default: "LM358N")
   - 8 buttons with colored emoji images:
     * 🔍 Search Component
     * 🗑️ Clear Results
     * ❓ Help
     * 🧠 Show Knowledge
     * 📋 Edit Websites
     * 📊 Load Excel File
     * 🧹 Cleanup Files
     * 🎓 Test Learning
   - Status fields for datasheet and 3D model results
   - Large scrollable text area with colored emoji images

MAIN WORKFLOW - <PERSON>ING<PERSON> COMPONENT SEARCH:
========================================

1. USER INPUT:
   - Enter manufacturer name (e.g., "Diodes Incorporated")
   - Enter part number (e.g., "APX803L20-30SA-7")
   - Click "🔍 Search Component"

2. DATASHEET SEARCH PROCESS:
   a) Check if manufacturer website is known in CSV database
   b) If unknown, search for manufacturer website via Google
   c) Search Digi-Key API for datasheet
   d) Search Mouser API for datasheet
   e) If not found, search manufacturer website directly
   f) Download PDF to datasheets/ folder
   g) Extract package type from datasheet if possible

3. 3D MODEL SEARCH PROCESS:
   a) Search manufacturer website first
   b) Search UltraLibrarian (ultralibrarian.com)
   c) Search SnapMagic/SnapEDA (snapmagic.com, snapeda.com)
   d) Search SamacSys (componentsearchengine.com)
   e) For WURTH parts: search we-online.com
   f) Download STEP file to 3d/ folder

4. RESULTS LOGGING:
   - Log all results to found-files-log.csv
   - Update knowledge base with successful patterns
   - Save manufacturer website to CSV if new
   - Display results in GUI with colored emoji status

EXCEL BATCH PROCESSING WORKFLOW:
===============================

1. LOAD EXCEL FILE:
   - Click "📊 Load Excel File"
   - Select .xlsx file with components
   - System reads Columns F (Manufacturer) and G (Part Number)

2. BATCH PROCESSING:
   - Click "🔍 Excel Part File Search"
   - Process each row automatically
   - For missing datasheets (Column H empty): search and download
   - For missing 3D models (Column K empty): search and download
   - Update Excel file with results:
     * Column H: Datasheet path
     * Column J: 3D model source
     * Column K: 3D model filename

3. PROGRESS TRACKING:
   - Real-time progress shown in text area
   - Each component search logged with colored emoji status
   - Excel file updated automatically as files are found

LEARNING SYSTEM:
===============

1. AUTOMATIC LEARNING:
   - System learns successful search patterns
   - Saves manufacturer websites automatically
   - Builds knowledge base of working URLs
   - Tracks success rates for different sources

2. MANUAL LEARNING (when automatic fails):
   - Opens webpage in browser
   - User manually finds download link
   - System learns the pattern for future use
   - Saves pattern to download_patterns.json

HELP SYSTEM:
===========

Click "❓ Help" to access comprehensive help system with tabs:
- Quick Start Guide
- Learning System Details
- Excel Setup Instructions
- Troubleshooting Guide
- About Information

All help content stored in editable text files in help_files/ directory.

FILES CREATED BY THE PROGRAM:
=============================

1. DIRECTORIES CREATED:
   - datasheets/          (Downloaded PDF datasheets)
   - 3d/                  (Downloaded STEP model files)
   - emoji_images/        (25 colored emoji PNG files)
   - help_files/          (Editable help text files)

2. DATABASE FILES:

   actual-web-site-xref.csv
   Fields: Manufacturer Name, Website
   Purpose: Maps manufacturer names to their websites
   Example: "Diodes Inc","https://www.diodes.com"

   manufacturer_knowledge.json
   Fields: manufacturer_key, name, base_url, search_url_format, search_patterns,
           datasheet_patterns, model_3d_patterns, package_detection, known_parts,
           last_successful_search, last_updated, success_count
   Purpose: Stores learned search patterns and successful URLs for each manufacturer

   found-files-log.csv
   Fields: Manufacturer Name, Part Number, Datasheet URL, Datasheet Filename,
           3D Model URL, 3D Model Filename, Package Type, Date Found, Search Success
   Purpose: Complete log of all search results and downloaded files

   download_patterns.json
   Fields: website, part_type, url_pattern, method, success_count, last_used
   Purpose: Stores learned download patterns from manual user interactions

3. CONFIGURATION FILES:

   digikey_api_credentials.json
   Fields: client_id, client_secret, redirect_uri
   Purpose: Digi-Key API access credentials

   component_site_credentials.json
   Fields: Various website login credentials (if needed)
   Purpose: Store credentials for manufacturer websites requiring login

   samacsys_credentials.json
   Fields: api_key, username, password
   Purpose: SamacSys API access credentials

4. HELP FILES (in help_files/ directory):
   - quick_start.txt       (Quick start guide)
   - learning_system.txt   (Learning system documentation)
   - excel_setup.txt       (Excel integration instructions)
   - troubleshooting.txt   (Common issues and solutions)
   - about.txt            (Program information)

5. EMOJI IMAGE FILES (in emoji_images/ directory):
   25 PNG files: rocket.png, check.png, cross.png, warning.png, search.png,
   download.png, info.png, art.png, chart.png, brain.png, clipboard.png,
   trash.png, question.png, broom.png, graduation.png, document.png,
   package.png, save.png, edit.png, wrench.png, bulb.png, target.png,
   star.png, folder.png, factory.png

6. DOWNLOADED FILES:
   - datasheets/*.pdf     (Component datasheets)
   - 3d/*.step           (3D STEP model files)
   - 3d/*.stp            (Alternative STEP extension)

EXCEL FILE INTEGRATION:
======================

REQUIRED COLUMNS (user provides):
- Column F: Manufacturer Name
- Column G: Part Number

UPDATED COLUMNS (program fills):
- Column H: Datasheet Path (e.g., "datasheets\Texas_Instruments_LM358N_datasheet.pdf")
- Column J: 3D Model Source (e.g., "UltraLibrarian" or "Manufacturer")
- Column K: 3D Model Filename (e.g., "LM358N.step")

SEARCH PRIORITY ORDER:
=====================

DATASHEETS:
1. Digi-Key API
2. Mouser API
3. Manufacturer website
4. Learning mode (manual user assistance)

3D MODELS:
1. Manufacturer website (direct search)
2. UltraLibrarian (ultralibrarian.com)
3. SnapMagic (snapmagic.com)
4. SnapEDA (snapeda.com)
5. SamacSys (componentsearchengine.com)
6. WURTH specific: we-online.com
7. Learning mode (manual user assistance)

ERROR HANDLING:
==============

- Network timeouts: Retry with different sources
- File not found: Try alternative search methods
- Download failures: Log error and continue with next source
- API rate limits: Implement delays and retry logic
- Invalid file formats: Skip and try next source
- Learning mode: Engage user when automatic methods fail

PROGRAM STATES:
==============

- Ready: Waiting for user input
- Searching: Actively searching for components
- Downloading: Downloading files
- Learning: Waiting for user manual assistance
- Processing Excel: Batch processing Excel file
- Error: Displaying error message and recovery options
