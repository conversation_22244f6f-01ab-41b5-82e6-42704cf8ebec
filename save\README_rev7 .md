# Component Finder - Web Get Files

🚀 **Automated Component Datasheet and 3D Model Finder**

## 📁 Directory Structure

```
e:\python\web-get-files\
├── component_finder_gui.py          # Main GUI application
├── run_component_finder.bat         # Easy run script for Windows
├── README.md                        # This file
├── datasheets\                      # Downloaded PDF datasheets
├── 3d\                             # Downloaded STEP 3D models
├── actual-web-site-xref.csv        # Manufacturer website mappings
├── found-files-log.csv             # Complete search history log
└── manufacturer_knowledge.json      # Learned search patterns
```

## 🚀 How to Run

### Method 1: Batch File (Easiest)
```bash
run_component_finder.bat
```

### Method 2: Command Line
```bash
python component_finder_gui.py
```

### Method 3: Alternative Python Command
```bash
py component_finder_gui.py
```

## 🎯 Quick Start

1. **Run the program** using one of the methods above
2. **Enter manufacturer**: "Diodes Inc"
3. **Enter part number**: "APX803L20-30SA-7"
4. **Click "🔍 Search Component"**
5. **Check results** in the status fields and datasheets/3d folders

## 📚 Features

- ✅ **Automated Search**: Finds datasheets and 3D models automatically
- ✅ **Learning System**: Gets smarter with each search
- ✅ **File Organization**: Saves files with manufacturer names
- ✅ **No Duplicates**: Automatically replaces old files with latest versions
- ✅ **CSV Tracking**: Complete log of all searches and found files
- ✅ **Manual Override**: Edit manufacturer websites manually
- ✅ **Comprehensive Help**: Built-in help system with detailed instructions

## 🔘 Button Guide

| Button | Purpose |
|--------|---------|
| 🔍 Search Component | Main search function |
| 🗑️ Clear Results | Clean comments area |
| 📚 Show Knowledge | Display learned manufacturers |
| 📋 Edit Websites | Edit manufacturer websites CSV |
| 🧹 Cleanup Files | Remove duplicate files |
| 📊 View Log | Preview search history |
| ❓ Help | Comprehensive help system |
| 📋 Found Files | Direct access to found files log |

## 📊 File Outputs

### Datasheets (PDF files)
- **Location**: `datasheets/` folder
- **Format**: `ManufacturerName PartNumber_datasheet.pdf`
- **Example**: `Diodes_Inc APX803L20-30SA-7_datasheet.pdf`

### 3D Models (STEP files)
- **Location**: `3d/` folder
- **Format**: `ManufacturerName PartNumber_PackageType.step`
- **Example**: `Diodes_Inc APX803L20-30SA-7_SOT23.step`

### CSV Files
- **actual-web-site-xref.csv**: Manufacturer to website mappings
- **found-files-log.csv**: Complete search history with URLs and results

## 🚨 Troubleshooting

### Program Won't Start
1. Make sure Python is installed
2. Make sure you're in the `e:\python\web-get-files` directory
3. Try the batch file: `run_component_finder.bat`
4. Check that `component_finder_gui.py` exists

### Search Fails
1. Check manufacturer name spelling
2. Try shorter manufacturer names (e.g., "TI" instead of "Texas Instruments")
3. Use "📋 Edit Websites" to add manufacturers manually
4. Click "❓ Help" for detailed troubleshooting

### Files Not Found
1. Check internet connection
2. Verify manufacturer provides files online
3. Try different part numbers from same manufacturer
4. Use manual download methods (see Help system)

## 💡 Tips

- **Start with known manufacturers**: Diodes Inc, Texas Instruments, Analog Devices
- **Use the help system**: Click "❓ Help" for comprehensive guidance
- **Check the log**: Use "📊 View Log" or "📋 Found Files" to track progress
- **Edit CSV files**: Add manufacturers manually for better results
- **Clean up regularly**: Use "🧹 Cleanup Files" to remove duplicates

## 🎓 Learning System

The program learns from each successful search:
- **First search**: Discovery mode (slower, learns patterns)
- **Subsequent searches**: Uses learned patterns (faster)
- **Knowledge base**: Stored in `manufacturer_knowledge.json`
- **CSV database**: Manufacturer websites in `actual-web-site-xref.csv`

## 📞 Need Help?

1. **Built-in Help**: Click "❓ Help" button in the GUI
2. **Check Comments**: Watch the comments area for detailed progress
3. **View Logs**: Use "📊 View Log" to see search history
4. **Manual Methods**: Help system includes manual download instructions

---

**Happy Component Hunting! 🎯📄🚀**
