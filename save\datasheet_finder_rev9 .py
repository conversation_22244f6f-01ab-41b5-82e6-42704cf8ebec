#!/usr/bin/env python3
"""
COMBINED DATASHEET FINDER
=========================
Tries multiple sources in order:
1. Digikey (API-based)
2. RS Components (web scraping)
3. User interaction for failed searches

Features:
- Tries Digikey first, then RS Components
- User interaction for failed searches
- CSV database with "not found" entries
- Structured return data

Usage:
    python datasheet_finder.py "Texas Instruments" "LM358N"
"""

import argparse
import sys
import os
import csv
from datetime import datetime

def log(message, level="INFO"):
    """Enhanced logging with timestamps"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    if level == "ERROR":
        print(f"❌ [{timestamp}] ERROR: {message}")
    elif level == "SUCCESS":
        print(f"✅ [{timestamp}] SUCCESS: {message}")
    elif level == "WARNING":
        print(f"⚠️ [{timestamp}] WARNING: {message}")
    else:
        print(f"ℹ️ [{timestamp}] INFO: {message}")

def update_csv_not_found(manufacturer, part_number):
    """Update CSV with 'not found' entry"""
    csv_file = "datasheets/actual-web-site-xref.csv"
    
    try:
        # Ensure datasheets directory exists
        os.makedirs("datasheets", exist_ok=True)
        
        # Read existing data
        existing_data = {}
        if os.path.exists(csv_file):
            with open(csv_file, 'r', newline='', encoding='utf-8') as f:
                reader = csv.reader(f)
                header = next(reader, None)  # Skip header
                for row in reader:
                    if len(row) >= 2:
                        # Handle different formats (2, 4, or 5 columns)
                        if len(row) >= 5:
                            existing_data[f"{row[0]}_{row[2]}"] = {
                                'manufacturer': row[0],
                                'website': row[1],
                                'part_number': row[2],
                                'package_type': row[3],
                                'datasheet_filename': row[4]
                            }
                        elif len(row) >= 4:
                            existing_data[f"{row[0]}_{row[2]}"] = {
                                'manufacturer': row[0],
                                'website': row[1],
                                'part_number': row[2],
                                'package_type': row[3],
                                'datasheet_filename': ''
                            }
                        else:
                            existing_data[row[0]] = {
                                'manufacturer': row[0],
                                'website': row[1],
                                'part_number': '',
                                'package_type': '',
                                'datasheet_filename': ''
                            }
        
        # Add the "not found" entry
        key = f"{manufacturer}_{part_number}"
        existing_data[key] = {
            'manufacturer': manufacturer,
            'website': 'not found',
            'part_number': part_number,
            'package_type': 'not found',
            'datasheet_filename': 'not found'
        }
        
        # Write back to file
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(["Manufacturer Name", "Website", "Part Number", "Package Type", "Datasheet Filename"])
            for key, data in sorted(existing_data.items()):
                writer.writerow([data['manufacturer'], data['website'], data['part_number'], 
                               data['package_type'], data['datasheet_filename']])
        
        log(f"Added 'not found' entry to CSV: {manufacturer} {part_number}", "SUCCESS")
        
    except Exception as e:
        log(f"Error updating CSV with 'not found' entry: {e}", "ERROR")

def check_for_similar_parts(result, manufacturer, part_number):
    """Check if we found similar parts and extract them"""
    similar_parts = []

    # Check Digikey results for similar parts
    if result.get('pdf_parsing') and result['pdf_parsing'].get('part_validation'):
        part_validation = result['pdf_parsing']['part_validation']
        if part_validation.get('found_parts'):
            similar_parts.extend(part_validation['found_parts'])

    # Check if this might be an incomplete part number
    incomplete_indicators = []

    # Method 1: Check if the found part is different from searched part
    if result.get('datasheet_file'):
        filename = result['datasheet_file']
        if filename:
            # Extract the part number from filename (between manufacturer and _datasheet)
            parts = filename.split('_')
            if len(parts) >= 3:
                found_part_in_filename = parts[1]  # Should be the part number
                if found_part_in_filename.upper() != part_number.upper():
                    # Check if searched part is a substring of found part
                    if part_number.upper() in found_part_in_filename.upper():
                        incomplete_indicators.append(f"Filename suggests {found_part_in_filename} vs searched {part_number}")

    # Method 2: Check if any similar parts contain our search term as a prefix
    if similar_parts:
        for similar_part in similar_parts:
            if (similar_part.upper().startswith(part_number.upper()) and
                len(similar_part) > len(part_number)):
                incomplete_indicators.append(f"Found longer part: {similar_part}")

    # Method 3: Check if part number looks incomplete (too short, no suffix)
    if len(part_number) < 8 and not any(suffix in part_number.upper() for suffix in ['-', 'N', 'A', 'P']):
        # Very short part numbers are often incomplete
        if similar_parts:
            incomplete_indicators.append(f"Short part number with similar parts available")

    # Determine if this looks like an incomplete part number
    is_incomplete = len(incomplete_indicators) > 0

    return is_incomplete, similar_parts

def get_user_input(original_manufacturer, original_part, similar_parts=None, incomplete_part=False):
    """Get user input for alternative manufacturer/part or retry decision"""
    print("\n" + "="*60)
    if incomplete_part:
        print("⚠️ INCOMPLETE PART NUMBER DETECTED")
        print("="*60)
        print(f"Searched for: {original_manufacturer} {original_part}")
        print("✅ Found datasheet, but this appears to be an incomplete part number.")
        print("💡 Complete part numbers usually have suffixes like -PU, -AU, -ND, etc.")
        if similar_parts:
            print(f"📋 Similar parts found: {', '.join(similar_parts[:5])}")
        print("\nThe downloaded datasheet may contain information about the complete part numbers.")
    else:
        print("🔍 DATASHEET NOT FOUND")
        print("="*60)
        print(f"Original search: {original_manufacturer} {original_part}")
        if similar_parts:
            print(f"📋 Similar parts found: {', '.join(similar_parts[:5])}")

    print("\nOptions:")
    print("1. Try different manufacturer name")
    print("2. Try different part number")
    print("3. Try both different manufacturer and part number")
    print("4. Retry with original information")
    if incomplete_part:
        print("5. Accept this result (incomplete part number but datasheet found)")
    else:
        print("5. Accept that part cannot be found")
    
    while True:
        try:
            choice = input("\nEnter your choice (1-5): ").strip()
            
            if choice == "1":
                new_manufacturer = input(f"Enter new manufacturer name (current: {original_manufacturer}): ").strip()
                if new_manufacturer:
                    return new_manufacturer, original_part, True
                else:
                    print("Invalid manufacturer name. Please try again.")
                    
            elif choice == "2":
                new_part = input(f"Enter new part number (current: {original_part}): ").strip()
                if new_part:
                    return original_manufacturer, new_part, True
                else:
                    print("Invalid part number. Please try again.")
                    
            elif choice == "3":
                new_manufacturer = input(f"Enter new manufacturer name (current: {original_manufacturer}): ").strip()
                new_part = input(f"Enter new part number (current: {original_part}): ").strip()
                if new_manufacturer and new_part:
                    return new_manufacturer, new_part, True
                else:
                    print("Invalid input. Please try again.")
                    
            elif choice == "4":
                return original_manufacturer, original_part, True
                
            elif choice == "5":
                return original_manufacturer, original_part, False
                
            else:
                print("Invalid choice. Please enter 1, 2, 3, 4, or 5.")
                
        except KeyboardInterrupt:
            print("\n\nOperation cancelled by user.")
            return original_manufacturer, original_part, False
        except EOFError:
            print("\n\nOperation cancelled.")
            return original_manufacturer, original_part, False

def find_datasheet_combined(manufacturer, part_number, interactive=True):
    """
    Combined datasheet finder - tries Digikey first, then RS Components
    
    Args:
        manufacturer (str): Manufacturer name
        part_number (str): Part number
        interactive (bool): Whether to prompt user for alternatives
        
    Returns:
        dict: Combined results from all sources
    """
    log(f"Starting combined datasheet search for: {manufacturer} {part_number}")
    log("="*80)
    
    original_manufacturer = manufacturer
    original_part = part_number
    attempt_count = 0
    max_attempts = 3
    
    while attempt_count < max_attempts:
        attempt_count += 1
        log(f"Attempt {attempt_count}: Searching for {manufacturer} {part_number}")
        
        # Step 1: Try Digikey
        log("Step 1: Trying Digikey...")
        try:
            from digikey_datasheet_improved import find_part_datasheet as digikey_search
            digikey_result = digikey_search(manufacturer, part_number)

            if digikey_result['success'] and digikey_result.get('datasheet_file'):
                log("Digikey found datasheet!", "SUCCESS")

                # Check if this might be an incomplete part number
                incomplete_part, similar_parts = check_for_similar_parts(digikey_result, manufacturer, part_number)

                if incomplete_part and interactive:
                    log("Detected possible incomplete part number", "WARNING")
                    manufacturer, part_number, retry = get_user_input(manufacturer, part_number, similar_parts, incomplete_part=True)

                    if not retry:
                        log("User accepted incomplete part number result", "INFO")
                        return {
                            'success': True,
                            'source': 'Digikey',
                            'manufacturer': original_manufacturer,
                            'part_number': original_part,
                            'message': f"Found datasheet via Digikey (incomplete part number): {original_manufacturer} {original_part}",
                            'datasheet_file': digikey_result['datasheet_file'],
                            'csv_file': digikey_result['csv_file'],
                            'pdf_parsing': digikey_result.get('pdf_parsing'),
                            'attempt_count': attempt_count,
                            'incomplete_part': True
                        }
                    elif manufacturer == original_manufacturer and part_number == original_part:
                        log("User chose to keep incomplete part number", "INFO")
                        return {
                            'success': True,
                            'source': 'Digikey',
                            'manufacturer': manufacturer,
                            'part_number': part_number,
                            'message': f"Found datasheet via Digikey (incomplete part number): {manufacturer} {part_number}",
                            'datasheet_file': digikey_result['datasheet_file'],
                            'csv_file': digikey_result['csv_file'],
                            'pdf_parsing': digikey_result.get('pdf_parsing'),
                            'attempt_count': attempt_count,
                            'incomplete_part': True
                        }
                    else:
                        log("User provided new search terms, continuing search...", "INFO")
                        continue
                else:
                    return {
                        'success': True,
                        'source': 'Digikey',
                        'manufacturer': manufacturer,
                        'part_number': part_number,
                        'message': f"Found datasheet via Digikey: {manufacturer} {part_number}",
                        'datasheet_file': digikey_result['datasheet_file'],
                        'csv_file': digikey_result['csv_file'],
                        'pdf_parsing': digikey_result.get('pdf_parsing'),
                        'attempt_count': attempt_count
                    }
            else:
                log("Digikey did not find datasheet", "WARNING")
                
        except Exception as e:
            log(f"Digikey search failed: {e}", "ERROR")
        
        # Step 2: Try RS Components
        log("Step 2: Trying RS Components...")
        try:
            from rs_components_scraper import find_part_datasheet as rs_search
            rs_result = rs_search(manufacturer, part_number)

            if rs_result['success'] and rs_result.get('datasheet_file'):
                log("RS Components found datasheet!", "SUCCESS")

                # Check if this might be an incomplete part number
                incomplete_part, similar_parts = check_for_similar_parts(rs_result, manufacturer, part_number)

                if incomplete_part and interactive:
                    log("Detected possible incomplete part number", "WARNING")
                    manufacturer, part_number, retry = get_user_input(manufacturer, part_number, similar_parts, incomplete_part=True)

                    if not retry:
                        log("User accepted incomplete part number result", "INFO")
                        return {
                            'success': True,
                            'source': 'RS Components',
                            'manufacturer': original_manufacturer,
                            'part_number': original_part,
                            'message': f"Found datasheet via RS Components (incomplete part number): {original_manufacturer} {original_part}",
                            'datasheet_file': rs_result['datasheet_file'],
                            'csv_file': rs_result['csv_file'],
                            'pdf_parsing': rs_result.get('pdf_parsing'),
                            'attempt_count': attempt_count,
                            'incomplete_part': True
                        }
                    elif manufacturer == original_manufacturer and part_number == original_part:
                        log("User chose to keep incomplete part number", "INFO")
                        return {
                            'success': True,
                            'source': 'RS Components',
                            'manufacturer': manufacturer,
                            'part_number': part_number,
                            'message': f"Found datasheet via RS Components (incomplete part number): {manufacturer} {part_number}",
                            'datasheet_file': rs_result['datasheet_file'],
                            'csv_file': rs_result['csv_file'],
                            'pdf_parsing': rs_result.get('pdf_parsing'),
                            'attempt_count': attempt_count,
                            'incomplete_part': True
                        }
                    else:
                        log("User provided new search terms, continuing search...", "INFO")
                        continue
                else:
                    return {
                        'success': True,
                        'source': 'RS Components',
                        'manufacturer': manufacturer,
                        'part_number': part_number,
                        'message': f"Found datasheet via RS Components: {manufacturer} {part_number}",
                        'datasheet_file': rs_result['datasheet_file'],
                        'csv_file': rs_result['csv_file'],
                        'pdf_parsing': rs_result.get('pdf_parsing'),
                        'attempt_count': attempt_count
                    }
            else:
                log("RS Components did not find datasheet", "WARNING")
                
        except Exception as e:
            log(f"RS Components search failed: {e}", "ERROR")
        
        # Step 3: User interaction (if interactive and not last attempt)
        if interactive and attempt_count < max_attempts:
            log("Both sources failed - asking user for alternatives...")
            manufacturer, part_number, retry = get_user_input(manufacturer, part_number, similar_parts=None, incomplete_part=False)

            if not retry:
                log("User chose to stop searching", "WARNING")
                break
            elif manufacturer == original_manufacturer and part_number == original_part:
                log("User chose to retry with same information", "INFO")
            else:
                log(f"User provided new search terms: {manufacturer} {part_number}", "INFO")
        else:
            log("All automatic attempts exhausted", "WARNING")
            break
    
    # No datasheet found - update CSV with "not found"
    log("No datasheet found from any source", "ERROR")
    update_csv_not_found(original_manufacturer, original_part)
    
    return {
        'success': False,
        'source': 'None',
        'manufacturer': original_manufacturer,
        'part_number': original_part,
        'message': f"Could not find datasheet for {original_manufacturer} {original_part} after {attempt_count} attempts",
        'datasheet_file': None,
        'csv_file': "datasheets/actual-web-site-xref.csv",
        'pdf_parsing': None,
        'attempt_count': attempt_count
    }

def main():
    parser = argparse.ArgumentParser(description='Combined datasheet finder (Digikey + RS Components)')
    parser.add_argument('manufacturer', help='Manufacturer name (e.g., "Texas Instruments")')
    parser.add_argument('part_number', help='Part number (e.g., "LM358N")')
    parser.add_argument('--no-interactive', action='store_true', help='Disable user interaction')
    
    args = parser.parse_args()
    
    interactive = not args.no_interactive
    result = find_datasheet_combined(args.manufacturer, args.part_number, interactive)
    
    # Display results
    print("\n" + "="*80)
    print("🎯 FINAL RESULTS")
    print("="*80)
    print(f"Success: {result['success']}")
    print(f"Source: {result['source']}")
    print(f"Manufacturer: {result['manufacturer']}")
    print(f"Part Number: {result['part_number']}")
    print(f"Message: {result['message']}")
    print(f"Attempts: {result['attempt_count']}")

    if result.get('incomplete_part'):
        print("⚠️ WARNING: This appears to be an incomplete part number")
        print("💡 Complete part numbers usually have suffixes like -PU, -AU, -ND, etc.")

    if result['datasheet_file']:
        print(f"Datasheet: {result['datasheet_file']}")

        if result.get('pdf_parsing'):
            pdf_info = result['pdf_parsing']
            if pdf_info.get('package_info') and pdf_info['package_info'].get('package'):
                print(f"Package: {pdf_info['package_info']['package']}")

            if pdf_info.get('part_validation') and pdf_info['part_validation'].get('found_parts'):
                similar_parts = pdf_info['part_validation']['found_parts']
                if similar_parts:
                    print(f"📋 Similar parts in datasheet: {', '.join(similar_parts[:5])}")

    if result['csv_file']:
        print(f"CSV Database: {result['csv_file']}")
    
    # Exit with appropriate code
    sys.exit(0 if result['success'] else 1)

if __name__ == "__main__":
    main()
