#!/usr/bin/env python3
"""
IMPROVED DIGI-KEY DATASHEET FINDER
==================================
Enhanced version with proper error handling, verification, and detailed output.

Features:
- Detailed step-by-step output
- Error handling with immediate stop
- File verification (size, format, content)
- Success/failure reporting
- Proper logging

Usage:
    python digikey_datasheet_improved.py "Texas Instruments" "LM358N"
"""

import requests
import json
import os
import sys
import time
import csv
from datetime import datetime
from urllib.parse import urlparse
import argparse

class DigikeyDatasheetFinder:
    def __init__(self):
        self.session = requests.Session()
        self.credentials = None
        self.datasheets_dir = "datasheets"
        self.manufacturer_websites_file = "manufacturer_websites.json"
        
    def log(self, message, level="INFO"):
        """Enhanced logging with timestamps"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        if level == "ERROR":
            print(f"❌ [{timestamp}] ERROR: {message}")
        elif level == "SUCCESS":
            print(f"✅ [{timestamp}] SUCCESS: {message}")
        elif level == "WARNING":
            print(f"⚠️ [{timestamp}] WARNING: {message}")
        else:
            print(f"ℹ️ [{timestamp}] INFO: {message}")
    
    def load_credentials(self):
        """Load and verify Digi-Key API credentials"""
        self.log("Loading Digi-Key API credentials...")
        
        try:
            if not os.path.exists('digikey_api_credentials.json'):
                self.log("Credentials file 'digikey_api_credentials.json' not found", "ERROR")
                return False
                
            with open('digikey_api_credentials.json', 'r') as f:
                self.credentials = json.load(f)
            
            # Verify required fields
            required_fields = ['client_id', 'access_token']
            for field in required_fields:
                if field not in self.credentials or not self.credentials[field]:
                    self.log(f"Missing or empty field '{field}' in credentials", "ERROR")
                    return False
            
            self.log("Credentials loaded successfully", "SUCCESS")
            return True
            
        except json.JSONDecodeError as e:
            self.log(f"Invalid JSON in credentials file: {e}", "ERROR")
            return False
        except Exception as e:
            self.log(f"Error loading credentials: {e}", "ERROR")
            return False
    
    def setup_directories(self):
        """Create necessary directories"""
        self.log("Setting up directories...")
        
        try:
            os.makedirs(self.datasheets_dir, exist_ok=True)
            self.log(f"Directory '{self.datasheets_dir}' ready", "SUCCESS")
            return True
        except Exception as e:
            self.log(f"Error creating directory: {e}", "ERROR")
            return False
    
    def refresh_token_if_needed(self):
        """Refresh Digi-Key token if expired"""
        self.log("Checking if token refresh is needed...")

        try:
            # Try to refresh token using digikey_token.py logic
            import subprocess
            result = subprocess.run([sys.executable, "digikey_token.py"],
                                  capture_output=True, text=True, timeout=60)

            if result.returncode == 0:
                self.log("Token refresh completed successfully", "SUCCESS")
                # Reload credentials
                return self.load_credentials()
            else:
                self.log(f"Token refresh failed: {result.stderr}", "ERROR")
                return False

        except Exception as e:
            self.log(f"Error refreshing token: {e}", "ERROR")
            return False

    def search_part(self, manufacturer, part_number):
        """Search for a part using Digi-Key API"""
        self.log(f"Searching for part: {manufacturer} {part_number}")

        # Try API call, refresh token if needed
        for attempt in range(2):  # Try twice: original token, then refreshed
            try:
                headers = {
                    'Authorization': f'Bearer {self.credentials["access_token"]}',
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-DIGIKEY-Client-Id': self.credentials["client_id"]
                }

                # Search URL
                search_url = "https://api.digikey.com/products/v4/search/keyword"

                # Search payload
                payload = {
                    "Keywords": part_number,
                    "RecordCount": 10,
                    "RecordStartPosition": 0,
                    "Filters": {
                        "ManufacturerFilter": [manufacturer] if manufacturer else []
                    }
                }

                self.log(f"Making API request to: {search_url} (attempt {attempt + 1})")
                response = self.session.post(search_url, headers=headers, json=payload, timeout=30)

                self.log(f"API Response Status: {response.status_code}")

                if response.status_code == 401 and attempt == 0:
                    self.log("API authentication failed - attempting token refresh", "WARNING")
                    if self.refresh_token_if_needed():
                        continue  # Try again with refreshed token
                    else:
                        return None
                elif response.status_code == 401:
                    self.log("API authentication failed even after token refresh", "ERROR")
                    return None
                elif response.status_code == 429:
                    self.log("API rate limit exceeded", "ERROR")
                    return None
                elif response.status_code != 200:
                    self.log(f"API request failed with status {response.status_code}: {response.text}", "ERROR")
                    return None

                # If we get here, the API call was successful
                data = response.json()

                if 'Products' not in data or not data['Products']:
                    self.log("No products found in API response", "WARNING")
                    return None

                self.log(f"Found {len(data['Products'])} products", "SUCCESS")
                return data['Products'][0]  # Return first match

            except requests.exceptions.Timeout:
                self.log("API request timed out", "ERROR")
                if attempt == 0:
                    continue  # Try again with token refresh
                return None
            except requests.exceptions.RequestException as e:
                self.log(f"API request error: {e}", "ERROR")
                if attempt == 0:
                    continue  # Try again with token refresh
                return None
            except json.JSONDecodeError as e:
                self.log(f"Invalid JSON response: {e}", "ERROR")
                if attempt == 0:
                    continue  # Try again with token refresh
                return None
            except Exception as e:
                self.log(f"Unexpected error during search: {e}", "ERROR")
                return None

        # If we get here, both attempts failed
        return None
    
    def extract_actual_pdf_url(self, datasheet_url):
        """Extract actual PDF URL from Digi-Key gateway URLs"""
        self.log("Extracting actual PDF URL...")

        try:
            from urllib.parse import urlparse, parse_qs, unquote

            # Parse the URL
            parsed = urlparse(datasheet_url)

            # Check if it's a gateway URL with gotoUrl parameter
            if 'gotoUrl' in parsed.query:
                query_params = parse_qs(parsed.query)
                if 'gotoUrl' in query_params:
                    # URL decode the gotoUrl parameter
                    actual_url = unquote(query_params['gotoUrl'][0])
                    self.log(f"Extracted actual URL: {actual_url}")
                    return actual_url

            # If no gotoUrl, return original URL
            return datasheet_url

        except Exception as e:
            self.log(f"Error extracting PDF URL: {e}", "WARNING")
            return datasheet_url

    def load_manufacturer_websites(self):
        """Load existing manufacturer websites database"""
        try:
            if os.path.exists(self.manufacturer_websites_file):
                with open(self.manufacturer_websites_file, 'r') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            self.log(f"Error loading manufacturer websites: {e}", "WARNING")
            return {}

    def save_manufacturer_website(self, manufacturer, pdf_url):
        """Save manufacturer website extracted from PDF URL"""
        try:
            # Extract website from PDF URL
            parsed_url = urlparse(pdf_url)
            website = parsed_url.netloc

            if not website:
                return

            self.log(f"Saving manufacturer website: {manufacturer} -> {website}")

            # Load existing data
            websites_data = self.load_manufacturer_websites()

            # Update with new information
            if manufacturer not in websites_data:
                websites_data[manufacturer] = {}

            websites_data[manufacturer].update({
                "website": website,
                "discovered_from": "digikey_api",
                "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "sample_datasheet_url": pdf_url
            })

            # Save back to file
            with open(self.manufacturer_websites_file, 'w') as f:
                json.dump(websites_data, f, indent=2)

            self.log(f"Manufacturer website saved successfully", "SUCCESS")

        except Exception as e:
            self.log(f"Error saving manufacturer website: {e}", "WARNING")

    def update_website_csv(self, manufacturer, website, part_number=None, package_type=None, datasheet_filename=None):
        """Update or create the actual-web-site-xref.csv file in datasheets directory"""
        csv_file = os.path.join(self.datasheets_dir, "actual-web-site-xref.csv")

        try:
            # Read existing data
            existing_data = {}
            if os.path.exists(csv_file):
                with open(csv_file, 'r', newline='', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    header = next(reader, None)  # Skip header
                    for row in reader:
                        if len(row) >= 2:
                            # Handle different formats (2, 4, or 5 columns)
                            if len(row) >= 5:
                                existing_data[row[0]] = {
                                    'website': row[1],
                                    'part_number': row[2],
                                    'package_type': row[3],
                                    'datasheet_filename': row[4]
                                }
                            elif len(row) >= 4:
                                existing_data[row[0]] = {
                                    'website': row[1],
                                    'part_number': row[2],
                                    'package_type': row[3],
                                    'datasheet_filename': ''
                                }
                            else:
                                existing_data[row[0]] = {
                                    'website': row[1],
                                    'part_number': '',
                                    'package_type': '',
                                    'datasheet_filename': ''
                                }

            # Add/update the manufacturer
            existing_data[manufacturer] = {
                'website': f"https://{website}",
                'part_number': part_number or '',
                'package_type': package_type or '',
                'datasheet_filename': datasheet_filename or ''
            }

            # Write back to file
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(["Manufacturer Name", "Website", "Part Number", "Package Type", "Datasheet Filename"])
                for mfg, data in sorted(existing_data.items()):
                    writer.writerow([mfg, data['website'], data['part_number'], data['package_type'], data['datasheet_filename']])

            self.log(f"Updated website CSV: {manufacturer} -> {website} (Part: {part_number}, Package: {package_type}, File: {datasheet_filename})", "SUCCESS")

        except Exception as e:
            self.log(f"Error updating website CSV: {e}", "WARNING")

    def download_datasheet(self, product, manufacturer, part_number):
        """Download datasheet from product data"""
        self.log("Looking for datasheet URL...")

        try:
            # Look for datasheet URL in product data
            datasheet_url = None

            if 'PrimaryDatasheet' in product and product['PrimaryDatasheet']:
                datasheet_url = product['PrimaryDatasheet']
            elif 'DatasheetUrl' in product and product['DatasheetUrl']:
                datasheet_url = product['DatasheetUrl']

            if not datasheet_url:
                self.log("No datasheet URL found in product data", "WARNING")
                return False

            self.log(f"Found datasheet URL: {datasheet_url}")

            # Extract actual PDF URL from gateway URLs
            actual_pdf_url = self.extract_actual_pdf_url(datasheet_url)

            # Fix URL scheme if missing
            if actual_pdf_url.startswith('//'):
                actual_pdf_url = 'https:' + actual_pdf_url
                self.log(f"Fixed URL scheme: {actual_pdf_url}")

            self.log(f"Using PDF URL: {actual_pdf_url}")

            # Save manufacturer website for future use
            self.save_manufacturer_website(manufacturer, actual_pdf_url)

            # Generate filename for CSV
            clean_manufacturer = manufacturer.replace(' ', '_').replace('/', '_')
            clean_part = part_number.replace('/', '_').replace(' ', '_')
            filename = f"{clean_manufacturer}_{clean_part}_datasheet.pdf"

            # Update the website CSV file (will be updated again with package info after PDF parsing)
            parsed_url = urlparse(actual_pdf_url)
            website = parsed_url.netloc
            if website:
                self.update_website_csv(manufacturer, website, part_number, None, filename)

            # Use the filename generated earlier
            filepath = os.path.join(self.datasheets_dir, filename)
            
            self.log(f"Downloading to: {filepath}")
            
            # Download the file with proper headers
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'application/pdf,*/*',
                'Referer': 'https://www.digikey.com/'
            }
            response = self.session.get(actual_pdf_url, timeout=60, stream=True, headers=headers)
            
            if response.status_code != 200:
                self.log(f"Download failed with status {response.status_code}", "ERROR")
                self.log(f"Response headers: {dict(response.headers)}", "ERROR")
                self.log(f"Full response content: {response.text}", "ERROR")

                # If it's a 403 error on mm.digikey.com, try alternative approach
                if response.status_code == 403 and 'mm.digikey.com' in actual_pdf_url:
                    self.log("Attempting alternative download method for Digikey-hosted PDF...", "INFO")
                    self.log(f"Original URL: {actual_pdf_url}")

                    # Extract any ID numbers from the URL
                    import re
                    url_parts = actual_pdf_url.split('/')
                    self.log(f"URL parts: {url_parts}")

                    # Look for numeric IDs in the URL
                    numeric_parts = [part for part in url_parts if re.search(r'\d+', part)]
                    self.log(f"Numeric parts found: {numeric_parts}")

                    # Try to access via main Digikey website with session cookies
                    main_digikey_url = "https://www.digikey.com/"
                    self.session.get(main_digikey_url, timeout=30)  # Get session cookies

                    # Try download again with session
                    response = self.session.get(actual_pdf_url, timeout=60, stream=True, headers=headers)

                    if response.status_code != 200:
                        self.log(f"Alternative download also failed with status {response.status_code}", "ERROR")
                        return False
                    else:
                        self.log("Alternative download method succeeded!", "SUCCESS")
                else:
                    return False
            
            # Check content type
            content_type = response.headers.get('content-type', '').lower()
            if 'pdf' not in content_type and not actual_pdf_url.lower().endswith('.pdf'):
                self.log(f"Warning: Content type is '{content_type}', may not be PDF", "WARNING")
            
            # Download with progress
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        if total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            print(f"\r  📥 Progress: {progress:.1f}% ({downloaded_size:,} / {total_size:,} bytes)", end='')
            
            print()  # New line after progress
            
            # Verify the downloaded file
            return self.verify_datasheet(filepath, filename)
            
        except Exception as e:
            self.log(f"Error downloading datasheet: {e}", "ERROR")
            return False
    
    def verify_datasheet(self, filepath, filename):
        """Verify that the downloaded file is valid"""
        self.log("Verifying downloaded file...")
        
        try:
            # Check if file exists
            if not os.path.exists(filepath):
                self.log("Downloaded file does not exist", "ERROR")
                return False
            
            # Check file size
            file_size = os.path.getsize(filepath)
            if file_size == 0:
                self.log("Downloaded file is empty", "ERROR")
                return False
            elif file_size < 1024:  # Less than 1KB
                self.log(f"Downloaded file is very small ({file_size} bytes) - may be invalid", "WARNING")
            
            # Check if it's actually a PDF
            with open(filepath, 'rb') as f:
                header = f.read(4)
                if header != b'%PDF':
                    self.log("Downloaded file is not a valid PDF", "ERROR")
                    return False
            
            self.log(f"File verified successfully: {filename} ({file_size:,} bytes)", "SUCCESS")
            return True
            
        except Exception as e:
            self.log(f"Error verifying file: {e}", "ERROR")
            return False
    
    def find_datasheet(self, manufacturer, part_number):
        """Main method to find and download datasheet"""
        self.log(f"Starting datasheet search for: {manufacturer} {part_number}")
        self.log("=" * 60)
        
        # Step 1: Load credentials
        if not self.load_credentials():
            self.log("Failed to load credentials - stopping", "ERROR")
            return False
        
        # Step 2: Setup directories
        if not self.setup_directories():
            self.log("Failed to setup directories - stopping", "ERROR")
            return False
        
        # Step 3: Search for part
        product = self.search_part(manufacturer, part_number)
        if not product:
            self.log("Failed to find part - stopping", "ERROR")
            return False
        
        # Step 4: Download datasheet
        success = self.download_datasheet(product, manufacturer, part_number)
        if not success:
            self.log("Failed to download datasheet - stopping", "ERROR")
            return False
        
        self.log("Datasheet download completed successfully!", "SUCCESS")
        return True

def find_part_datasheet(manufacturer, part_number):
    """
    Callable function to find and download datasheet

    Args:
        manufacturer (str): Manufacturer name (e.g., "Texas Instruments")
        part_number (str): Part number (e.g., "LM358N")

    Returns:
        dict: {
            'success': bool,
            'message': str,
            'datasheet_file': str or None,
            'csv_file': str or None
        }
    """
    try:
        finder = DigikeyDatasheetFinder()
        success = finder.find_datasheet(manufacturer, part_number)

        if success:
            # Generate expected filenames
            clean_manufacturer = manufacturer.replace(' ', '_').replace('/', '_')
            clean_part = part_number.replace('/', '_').replace(' ', '_')
            datasheet_file = f"datasheets/{clean_manufacturer}_{clean_part}_datasheet.pdf"
            csv_file = "datasheets/actual-web-site-xref.csv"

            # Parse the PDF to validate part and extract package
            try:
                from pdf_parser import parse_datasheet_pdf
                pdf_results = parse_datasheet_pdf(datasheet_file, part_number)

                # Update CSV with package type if found
                if (pdf_results.get('success') and
                    pdf_results.get('package_info') and
                    pdf_results['package_info'].get('package')):

                    package_type = pdf_results['package_info']['package']

                    # Update CSV with package information
                    from urllib.parse import urlparse
                    # We need to get the website again for the update
                    finder_instance = DigikeyDatasheetFinder()
                    websites_data = finder_instance.load_manufacturer_websites()
                    if manufacturer in websites_data:
                        website = websites_data[manufacturer].get('website', '')
                        if website:
                            website = website.replace('https://', '').replace('http://', '')
                            # Generate filename for the CSV update
                            clean_manufacturer = manufacturer.replace(' ', '_').replace('/', '_')
                            clean_part = part_number.replace('/', '_').replace(' ', '_')
                            filename = f"{clean_manufacturer}_{clean_part}_datasheet.pdf"
                            finder_instance.update_website_csv(manufacturer, website, part_number, package_type, filename)

                return {
                    'success': True,
                    'message': f"Successfully downloaded datasheet for {manufacturer} {part_number}",
                    'datasheet_file': datasheet_file,
                    'csv_file': csv_file,
                    'pdf_parsing': pdf_results
                }
            except Exception as e:
                return {
                    'success': True,
                    'message': f"Downloaded datasheet but PDF parsing failed: {str(e)}",
                    'datasheet_file': datasheet_file,
                    'csv_file': csv_file,
                    'pdf_parsing': None
                }
        else:
            return {
                'success': False,
                'message': f"Failed to download datasheet for {manufacturer} {part_number}",
                'datasheet_file': None,
                'csv_file': None
            }

    except Exception as e:
        return {
            'success': False,
            'message': f"Error: {str(e)}",
            'datasheet_file': None,
            'csv_file': None
        }

def main():
    parser = argparse.ArgumentParser(description='Download datasheet from Digi-Key')
    parser.add_argument('manufacturer', help='Manufacturer name (e.g., "Texas Instruments")')
    parser.add_argument('part_number', help='Part number (e.g., "LM358N")')
    
    args = parser.parse_args()
    
    finder = DigikeyDatasheetFinder()
    success = finder.find_datasheet(args.manufacturer, args.part_number)
    
    if success:
        print(f"\n🎉 SUCCESS: Datasheet downloaded for {args.manufacturer} {args.part_number}")
        sys.exit(0)
    else:
        print(f"\n💥 FAILED: Could not download datasheet for {args.manufacturer} {args.part_number}")
        sys.exit(1)

if __name__ == "__main__":
    main()
