#!/usr/bin/env python3
"""
SamacSys 3D Finder - Selenium-based with GUI and CSV logging
"""

import os
import sys
import time
import csv
import zipfile
import tkinter as tk
from tkinter import messagebox, ttk
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from samacsys_credentials import get_credentials

def log_to_csv(manufacturer, part_number, source, step_filename):
    """Log the download information to CSV file - prevents duplicates"""
    csv_filename = os.path.join("3d", "3d_model_downloads.csv")

    # Check for duplicates by reading existing entries
    if os.path.exists(csv_filename):
        try:
            with open(csv_filename, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                for row in reader:
                    if (row['MANUFACTURER'].lower() == manufacturer.lower() and
                        row['PART_NUMBER'].lower() == part_number.lower() and
                        row['SOURCE'].lower() == source.lower()):
                        print(f"ℹ️ Entry already exists in CSV: {manufacturer}, {part_number}, {source}")
                        return
        except Exception as e:
            print(f"⚠️ Could not read CSV for duplicate check: {e}")

    # Check if file exists to determine if we need headers
    file_exists = os.path.exists(csv_filename)

    try:
        with open(csv_filename, 'a', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['MANUFACTURER', 'PART_NUMBER', 'SOURCE', 'STEP_FILE_NAME']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            # Write header if file is new
            if not file_exists:
                writer.writeheader()
                print(f"✅ Created new CSV file: {csv_filename}")

            # Write the data
            writer.writerow({
                'MANUFACTURER': manufacturer,
                'PART_NUMBER': part_number,
                'SOURCE': source,
                'STEP_FILE_NAME': step_filename
            })

            print(f"✅ Logged to CSV: {manufacturer}, {part_number}, {source}, {step_filename}")

    except Exception as e:
        print(f"⚠️ Could not write to CSV: {e}")

def show_alternatives_gui(original_part, alternatives):
    """Show GUI for selecting alternatives"""
    class AlternativeSelector:
        def __init__(self):
            self.selected_index = None
            self.cancelled = False
            self.checkbox_vars = []

        def show_dialog(self):
            root = tk.Tk()
            root.title("SamacSys Alternative Selection")
            root.geometry("600x400")
            root.resizable(True, True)

            # Center the window
            root.update_idletasks()
            x = (root.winfo_screenwidth() // 2) - (600 // 2)
            y = (root.winfo_screenheight() // 2) - (400 // 2)
            root.geometry(f"600x400+{x}+{y}")

            # Main frame
            main_frame = ttk.Frame(root, padding="20")
            main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

            # Title
            title_label = ttk.Label(main_frame, text="SamacSys 3D Model Finder",
                                  font=("Arial", 16, "bold"))
            title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))

            # Original part info
            info_label = ttk.Label(main_frame,
                                 text=f"Original part '{original_part}' has no 3D model.\nSelect alternative(s):",
                                 font=("Arial", 11))
            info_label.grid(row=1, column=0, columnspan=2, pady=(0, 15))

            # Checkboxes for alternatives
            checkbox_frame = ttk.Frame(main_frame)
            checkbox_frame.grid(row=3, column=0, columnspan=2, pady=(0, 20), sticky=(tk.W, tk.E))

            # Create scrollable frame for checkboxes
            canvas = tk.Canvas(checkbox_frame, height=150)
            scrollbar = ttk.Scrollbar(checkbox_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = ttk.Frame(canvas)

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
            scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

            # Create checkboxes for each alternative
            self.checkbox_vars = []
            for i, (text, href) in enumerate(alternatives):
                var = tk.BooleanVar()
                checkbox = ttk.Checkbutton(scrollable_frame,
                                         text=f"{i+1}. {text}",
                                         variable=var)
                checkbox.grid(row=i, column=0, sticky=tk.W, pady=2, padx=5)
                self.checkbox_vars.append(var)

            # Buttons
            button_frame = ttk.Frame(main_frame)
            button_frame.grid(row=4, column=0, columnspan=2, pady=(10, 0))

            def on_select():
                selected_indices = []
                for i, var in enumerate(self.checkbox_vars):
                    if var.get():
                        selected_indices.append(i)

                if selected_indices:
                    self.selected_index = selected_indices
                    root.destroy()
                else:
                    messagebox.showwarning("No Selection", "Please select at least one alternative part.")

            def on_cancel():
                self.cancelled = True
                root.destroy()

            # Buttons
            select_btn = ttk.Button(button_frame, text="Use Selected Alternatives",
                                  command=on_select, width=22)
            select_btn.grid(row=0, column=0, padx=(0, 10))

            cancel_btn = ttk.Button(button_frame, text="Cancel",
                                  command=on_cancel, width=15)
            cancel_btn.grid(row=0, column=1)

            # Configure grid weights
            root.columnconfigure(0, weight=1)
            root.rowconfigure(0, weight=1)
            main_frame.columnconfigure(0, weight=1)
            checkbox_frame.columnconfigure(0, weight=1)

            # Show dialog - fix taskbar problem
            root.lift()
            root.attributes('-topmost', True)
            root.after(100, lambda: root.attributes('-topmost', False))
            root.focus_force()
            root.grab_set()
            root.mainloop()

            return self.selected_index, self.cancelled

    selector = AlternativeSelector()
    return selector.show_dialog()

class WorkingSamacSys:
    def __init__(self):
        self.base_url = 'https://componentsearchengine.com'
        self.email = None
        self.password = None
        self.logged_in = False
        os.makedirs('3d', exist_ok=True)

    def setup_driver(self):
        """Setup Chrome driver"""
        chrome_options = Options()
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Use same download preferences as UltraLibrarian
        download_dir = os.path.abspath('3d')
        os.makedirs(download_dir, exist_ok=True)

        prefs = {
            "download.default_directory": download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            return driver
        except Exception as e:
            print(f"Chrome driver setup failed: {e}")
            return None

    def search_and_download(self, manufacturer, part_number):
        """Complete search and download process"""
        print(f"\n🔸 STEP 1: Starting SamacSys search...")
        print(f"   Manufacturer: {manufacturer}")
        print(f"   Part Number: {part_number}")

        # Flag to prevent multiple downloads
        self.download_attempted = False

        driver = self.setup_driver()
        if not driver:
            return None
        
        try:
            # Step 1: Login to SamacSys
            print(f"\n🔸 STEP 1A: Logging into SamacSys...")
            if not self.login_to_samacsys(driver):
                print("❌ Login failed - cannot proceed")
                return None

            # Step 2: Go to homepage and search
            print(f"\n🔸 STEP 1B: Going to homepage...")
            driver.get(self.base_url)
            time.sleep(5)

            # Find and fill search box
            print(f"   🔍 Searching for: {part_number}")
            search_selectors = [
                "//input[@name='term' and @class='input-field is-large']",
                "//input[@name='term']",
                "//input[contains(@placeholder, 'Search')]"
            ]

            search_filled = False
            for selector in search_selectors:
                try:
                    search_boxes = driver.find_elements(By.XPATH, selector)
                    for search_box in search_boxes:
                        if search_box.is_displayed() and search_box.is_enabled():
                            search_box.clear()
                            search_box.send_keys(part_number)
                            search_box.send_keys(Keys.RETURN)
                            time.sleep(8)
                            print(f"   ✅ Search submitted")
                            search_filled = True
                            break
                    if search_filled:
                        break
                except:
                    continue

            if not search_filled:
                print(f"   ❌ Could not find search box")
                return None

            # Step 3: Click the SamacSys result (not FindChips)
            print(f"\n🔸 STEP 1C: Looking for SamacSys result...")

            # Look for SamacSys-specific results
            samacsys_result_selectors = [
                "//a[contains(@href, 'part-view') and contains(@href, 'componentsearchengine.com')]",
                "//a[contains(@href, '/part-view/')]",
                "//div[contains(@class, 'part-result')]//a[contains(@href, 'part-view')]",
                "//tr[contains(@class, 'search-row')]//a[contains(@href, 'part-view')]"
            ]

            clicked_result = False
            for selector in samacsys_result_selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed():
                            href = element.get_attribute('href') or ''
                            text = element.text.strip()

                            # Make sure it's not a FindChips or other external link
                            if 'findchips' not in href.lower() and 'componentsearchengine.com' in href:
                                print(f"   ✅ Found SamacSys result: {text}")
                                element.click()
                                time.sleep(8)
                                print(f"   ✅ Clicked SamacSys result")
                                clicked_result = True
                                break
                    if clicked_result:
                        break
                except:
                    continue

            if not clicked_result:
                print(f"   ❌ Could not find SamacSys result")
                return None
            
            # Step 2: Look for 3D model availability
            print(f"\n🔸 STEP 2: Checking for 3D model...")
            page_text = driver.page_source.lower()
            
            if '3d model' not in page_text and 'step' not in page_text:
                print(f"   ❌ No 3D model found for {part_number}")
                return None
            
            print(f"   ✅ 3D model available")
            
            # Step 3: Look for download links
            print(f"\n🔸 STEP 3: Looking for download links...")
            
            download_selectors = [
                "//a[contains(@class, 'ecad-model-button')]",
                "//i[contains(@class, 'download')]/..",
                "//a[contains(text(), 'Download') and contains(text(), 'Model')]"
            ]
            
            download_clicked = False
            for selector in download_selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed():
                            text = element.text.strip()
                            href = element.get_attribute('href') or ''

                            print(f"   🔍 DEBUG: Found element: '{text[:30]}...' -> {href[:60]}...")

                            # Allow tracking URLs for downloads but avoid ads
                            if (any(word in text.lower() for word in ['download', 'step', '3d']) or \
                               any(word in href.lower() for word in ['download', 'step', '3d'])) and \
                               'ads.' not in href and \
                               ('model' in text.lower() or 'download' in text.lower()):

                                # Check if download already attempted
                                if self.download_attempted:
                                    print(f"   ⚠️ Download already attempted, skipping...")
                                    continue

                                print(f"   ✅ Found valid download link: {text} -> {href[:50]}...")

                                # Record time BEFORE clicking
                                import time as time_module
                                download_start_time = time_module.time()

                                element.click()
                                time.sleep(3)
                                download_clicked = True
                                self.download_attempted = True

                                # Handle any download dialogs that appear
                                print(f"   🔸 Checking for download dialogs...")
                                self.handle_download_dialogs(driver)

                                print(f"   ✅ Download initiated - monitoring for download...")

                                # Monitor for download directly
                                return self.monitor_download_simple(driver, manufacturer, part_number, download_start_time)

                        if download_clicked:
                            break
                except:
                    continue

                if download_clicked:
                    break
            
            # This should never be reached due to return statements above
            print(f"   ❌ No download links found")
            return None
            
        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()
            return None
        finally:
            if hasattr(self, 'manual_mode_used') and self.manual_mode_used:
                input("Press Enter to close browser...")
            driver.quit()

    def handle_download_dialogs(self, driver):
        """Handle download confirmation dialogs that may appear"""
        try:
            # Wait a moment for any dialogs to appear
            time.sleep(2)

            # Try to handle common download dialog patterns
            dialog_selectors = [
                "//button[contains(text(), 'Save')]",
                "//button[contains(text(), 'Download')]",
                "//button[contains(text(), 'OK')]",
                "//button[contains(text(), 'Confirm')]",
                "//input[@type='submit' and contains(@value, 'Save')]",
                "//input[@type='submit' and contains(@value, 'Download')]",
                "//a[contains(text(), 'Save')]",
                "//a[contains(text(), 'Download')]"
            ]

            dialog_handled = False
            for selector in dialog_selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            print(f"   ✅ Found download dialog button: {element.text.strip()}")
                            element.click()
                            time.sleep(2)
                            dialog_handled = True
                            break
                    if dialog_handled:
                        break
                except:
                    continue

            if dialog_handled:
                print(f"   ✅ Download dialog handled")
            else:
                print(f"   ℹ️ No download dialog found (may be browser-level)")

        except Exception as e:
            print(f"   ⚠️ Error handling download dialog: {e}")

    def login_to_samacsys(self, driver):
        """Login to SamacSys/ComponentSearchEngine"""
        try:
            print(f"🔐 Logging into SamacSys...")

            # Get credentials
            if not self.email or not self.password:
                self.email, self.password = get_credentials()
                if not self.email or not self.password:
                    print("❌ No valid credentials provided")
                    return False

            # Clear cookies and go to login page fresh
            driver.delete_all_cookies()
            login_url = f"{self.base_url}/signin"
            driver.get(login_url)
            time.sleep(8)
            print(f"   🔍 Loaded login page: {driver.current_url}")

            # Check if already logged in by looking for login form
            login_forms = driver.find_elements(By.XPATH, "//form[contains(@action, 'login') or contains(@action, 'signin')]")
            email_fields = driver.find_elements(By.XPATH, "//input[@type='email']")

            if not login_forms and not email_fields:
                print("✅ Already logged in (no login form found)")
                self.logged_in = True
                return True
            elif "login" not in driver.current_url.lower() and "signin" not in driver.current_url.lower():
                print("✅ Already logged in (not on login page)")
                self.logged_in = True
                return True

            # Fill login form
            email_selectors = [
                "//input[@type='email']",
                "//input[contains(@name, 'email')]",
                "//input[contains(@id, 'email')]",
                "//input[contains(@placeholder, 'email')]"
            ]

            email_filled = False
            for selector in email_selectors:
                try:
                    email_field = driver.find_element(By.XPATH, selector)
                    if email_field.is_displayed():
                        email_field.clear()
                        email_field.send_keys(self.email)
                        print(f"✅ Filled email field")
                        email_filled = True
                        break
                except:
                    continue

            if not email_filled:
                print("❌ Could not find email field")
                return False

            # Fill password field
            password_selectors = [
                "//input[@type='password']",
                "//input[contains(@name, 'password')]",
                "//input[contains(@id, 'password')]"
            ]

            password_filled = False
            for selector in password_selectors:
                try:
                    password_field = driver.find_element(By.XPATH, selector)
                    if password_field.is_displayed():
                        password_field.clear()
                        password_field.send_keys(self.password)
                        print(f"✅ Filled password field")
                        password_filled = True
                        break
                except:
                    continue

            if not password_filled:
                print("❌ Could not find password field")
                return False

            # Submit login form
            submit_selectors = [
                "//button[@type='submit']",
                "//input[@type='submit']",
                "//button[contains(text(), 'Login')]",
                "//button[contains(text(), 'Sign')]",
                "//a[contains(text(), 'Login')]"
            ]

            login_submitted = False
            for selector in submit_selectors:
                try:
                    submit_button = driver.find_element(By.XPATH, selector)
                    if submit_button.is_displayed():
                        submit_button.click()
                        print(f"✅ Clicked login button")
                        login_submitted = True
                        break
                except:
                    continue

            if not login_submitted:
                # Try pressing Enter on password field
                try:
                    password_field.send_keys(Keys.RETURN)
                    print(f"✅ Pressed Enter to submit")
                    login_submitted = True
                except:
                    print("❌ Could not submit login form")
                    return False

            # Wait for login to complete
            time.sleep(8)

            # Check if login was successful
            current_url = driver.current_url.lower()
            if "login" not in current_url and "signin" not in current_url and "register" not in current_url and "error" not in current_url:
                print("✅ Login successful")
                self.logged_in = True
                return True
            else:
                print(f"❌ Login failed - still on: {driver.current_url}")
                print("   This might be due to:")
                print("   - Incorrect credentials")
                print("   - Account verification required")
                print("   - Site maintenance")
                return False

        except Exception as e:
            print(f"❌ Login error: {e}")
            return False

    def handle_download_screen(self, driver, manufacturer, part_number):
        """Handle the download screen that appears after clicking download"""
        try:
            print(f"   🔸 Step 4A: Analyzing download screen...")
            time.sleep(5)

            current_url = driver.current_url
            print(f"   🔍 Current URL: {current_url}")

            # Check if we're on a registration page
            if 'register' in current_url.lower() or 'login' in current_url.lower():
                print(f"   ⚠️ Registration/login required - attempting to handle...")

                # Look for "Continue as Guest" or similar options
                guest_options = [
                    "//a[contains(text(), 'guest')]",
                    "//button[contains(text(), 'guest')]",
                    "//a[contains(text(), 'continue')]",
                    "//button[contains(text(), 'continue')]",
                    "//a[contains(text(), 'skip')]",
                    "//button[contains(text(), 'skip')]"
                ]

                guest_clicked = False
                for selector in guest_options:
                    try:
                        elements = driver.find_elements(By.XPATH, selector)
                        for element in elements:
                            if element.is_displayed():
                                text = element.text.strip()
                                print(f"   🔍 Found guest option: {text}")
                                element.click()
                                time.sleep(5)
                                guest_clicked = True
                                break
                        if guest_clicked:
                            break
                    except:
                        continue

                if not guest_clicked:
                    print(f"   ❌ Could not bypass registration")
                    return None

            # Look for and fill required form fields
            print(f"   🔸 Step 4B: Looking for required form fields...")

            # Fill email field if present (common requirement)
            email_selectors = [
                "//input[@type='email']",
                "//input[contains(@name, 'email')]",
                "//input[contains(@id, 'email')]",
                "//input[contains(@placeholder, 'email')]"
            ]

            for selector in email_selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            print(f"   🔍 Found email field, filling with test email...")
                            element.clear()
                            element.send_keys("<EMAIL>")
                            time.sleep(2)
                            break
                except:
                    continue

            # Fill company field if present
            company_selectors = [
                "//input[contains(@name, 'company')]",
                "//input[contains(@id, 'company')]",
                "//input[contains(@placeholder, 'company')]"
            ]

            for selector in company_selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            print(f"   🔍 Found company field, filling...")
                            element.clear()
                            element.send_keys("Test Company")
                            time.sleep(2)
                            break
                except:
                    continue

            # Check any required checkboxes (terms, etc.)
            checkbox_selectors = [
                "//input[@type='checkbox' and @required]",
                "//input[@type='checkbox'][contains(@name, 'terms')]",
                "//input[@type='checkbox'][contains(@name, 'agree')]"
            ]

            for selector in checkbox_selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled() and not element.is_selected():
                            print(f"   🔍 Found required checkbox, checking...")
                            element.click()
                            time.sleep(2)
                            break
                except:
                    continue

            # Look for Download Model button on the part view page
            print(f"   🔸 Step 4C: Looking for Download Model button...")



            download_model_selectors = [
                f"//a[contains(text(), 'Download {part_number} Model')]",
                "//a[contains(@class, 'ecad-model-button')]",
                "//a[contains(@class, 'ecad-model-button button primary')]",
                "//a[contains(text(), 'Download') and contains(text(), 'Model')]"
            ]

            download_clicked = False
            for selector in download_model_selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed():
                            text = element.text.strip() or element.get_attribute('value') or ''
                            print(f"   ✅ Found Download Model button: {text}")

                            # Try regular click first
                            try:
                                element.click()
                                print(f"   ✅ Clicked Download Model (regular click)")
                                time.sleep(8)
                                download_clicked = True
                                break
                            except Exception as e:
                                print(f"   ⚠️ Regular click failed: {e}")
                                # Try JavaScript click
                                try:
                                    driver.execute_script("arguments[0].click();", element)
                                    print(f"   ✅ Clicked Download Model (JavaScript click)")
                                    time.sleep(8)
                                    download_clicked = True
                                    break
                                except Exception as e2:
                                    print(f"   ❌ JavaScript click also failed: {e2}")
                                    continue
                    if download_clicked:
                        break
                except:
                    continue

            if download_clicked:
                print(f"   ✅ Clicked Download Model button")

                # Monitor for download with shorter timeout since we handled the screen
                return self.monitor_download_after_screen(driver, manufacturer, part_number)
            else:
                print(f"   ❌ Could not find Download Model button with any selector")
                return None

        except Exception as e:
            print(f"   ❌ Error handling download screen: {e}")
            return None

    def monitor_download_after_screen(self, driver, manufacturer, part_number):
        """Monitor for download after handling the download screen"""
        print(f"   🔸 Step 4D: Monitoring for download after screen handling...")
        downloads_dir = os.path.expanduser("~/Downloads")
        initial_files = set(os.listdir(downloads_dir)) if os.path.exists(downloads_dir) else set()

        for i in range(12):  # 1 minute - shorter since we handled the screen
            time.sleep(5)
            print(f"   ⏳ Checking... ({(i+1)*5}/60 seconds)")

            current_files = set(os.listdir(downloads_dir)) if os.path.exists(downloads_dir) else set()
            new_files = current_files - initial_files

            # Check for any new ZIP files first (SamacSys downloads ZIP files)
            zip_files = [f for f in new_files if f.lower().endswith('.zip')]

            if zip_files:
                zip_file = zip_files[0]
                print(f"   📦 ZIP file downloaded: {zip_file}")

                # Extract ZIP and look for STEP files
                zip_path = os.path.join(downloads_dir, zip_file)
                import zipfile

                try:
                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                        # Extract to temporary directory
                        temp_extract_dir = os.path.join('3d', 'temp_extract')
                        os.makedirs(temp_extract_dir, exist_ok=True)
                        zip_ref.extractall(temp_extract_dir)
                        print(f"   📂 Extracted ZIP contents")

                        # Find STEP files in extracted content
                        step_files = []
                        for root, dirs, files in os.walk(temp_extract_dir):
                            for file in files:
                                if file.lower().endswith(('.step', '.stp')):
                                    step_files.append(os.path.join(root, file))

                        if step_files:
                            # Use the first STEP file found
                            step_file_path = step_files[0]
                            step_file_name = os.path.basename(step_file_path)
                            print(f"   ✅ Found STEP file in ZIP: {step_file_name}")

                            # Move to 3d directory with proper naming
                            new_name = f"SamacSys-{manufacturer}-{part_number}.step"
                            dst_path = os.path.join('3d', new_name)

                            # Remove existing file if it exists
                            if os.path.exists(dst_path):
                                os.remove(dst_path)
                                print(f"   🔍 Removed existing file: {new_name}")

                            import shutil
                            shutil.move(step_file_path, dst_path)
                            print(f"   ✅ Moved STEP file to: 3d/{new_name}")

                            # Clean up temp directory
                            shutil.rmtree(temp_extract_dir)

                            # Remove the ZIP file
                            os.remove(zip_path)

                            # Log to CSV
                            log_to_csv(manufacturer, part_number, "SAMACSYS", new_name)

                            return new_name
                        else:
                            print(f"   ❌ No STEP files found in ZIP")
                            # Clean up temp directory
                            import shutil
                            shutil.rmtree(temp_extract_dir)

                except Exception as e:
                    print(f"   ❌ Error extracting ZIP: {e}")

            # Also check for direct STEP files (fallback)
            step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]

            if step_files:
                step_file = step_files[0]
                print(f"   ✅ STEP file downloaded: {step_file}")

                # Move to 3d directory with proper naming
                src_path = os.path.join(downloads_dir, step_file)
                new_name = f"SamacSys-{manufacturer}-{part_number}.step"
                dst_path = os.path.join('3d', new_name)

                # Remove existing file if it exists
                if os.path.exists(dst_path):
                    os.remove(dst_path)
                    print(f"   🔍 Removed existing file: {new_name}")

                import shutil
                shutil.move(src_path, dst_path)
                print(f"   ✅ Moved to: 3d/{new_name}")

                # Log to CSV
                log_to_csv(manufacturer, part_number, "SAMACSYS", new_name)

                return new_name

        print(f"   ❌ No files downloaded after 1 minute")
        return None

    def monitor_manual_download(self, driver, manufacturer, part_number):
        """Monitor for manual download - user clicks the button manually"""
        print(f"   🔸 Step 4D: Monitoring for manual download...")
        downloads_dir = os.path.expanduser("~/Downloads")
        initial_files = set(os.listdir(downloads_dir)) if os.path.exists(downloads_dir) else set()

        for i in range(60):  # 5 minutes for manual intervention
            time.sleep(5)

            current_files = set(os.listdir(downloads_dir)) if os.path.exists(downloads_dir) else set()
            new_files = current_files - initial_files

            # Check for any new ZIP files first (SamacSys downloads ZIP files)
            zip_files = [f for f in new_files if f.lower().endswith('.zip')]

            if zip_files:
                zip_file = zip_files[0]
                print(f"   📦 ZIP file downloaded: {zip_file}")

                # Extract ZIP and look for STEP files
                zip_path = os.path.join(downloads_dir, zip_file)
                import zipfile

                try:
                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                        # Extract to temporary directory
                        temp_extract_dir = os.path.join('3d', 'temp_extract')
                        os.makedirs(temp_extract_dir, exist_ok=True)
                        zip_ref.extractall(temp_extract_dir)
                        print(f"   📂 Extracted ZIP contents")

                        # Find STEP files in extracted content
                        step_files = []
                        for root, dirs, files in os.walk(temp_extract_dir):
                            for file in files:
                                if file.lower().endswith(('.step', '.stp')):
                                    step_files.append(os.path.join(root, file))

                        if step_files:
                            # Use the first STEP file found
                            step_file_path = step_files[0]
                            step_file_name = os.path.basename(step_file_path)
                            print(f"   ✅ Found STEP file in ZIP: {step_file_name}")

                            # Move to 3d directory with proper naming
                            new_name = f"SamacSys-{manufacturer}-{part_number}.step"
                            dst_path = os.path.join('3d', new_name)

                            # Remove existing file if it exists
                            if os.path.exists(dst_path):
                                os.remove(dst_path)
                                print(f"   🔍 Removed existing file: {new_name}")

                            import shutil
                            shutil.move(step_file_path, dst_path)
                            print(f"   ✅ Moved STEP file to: 3d/{new_name}")

                            # Clean up temp directory
                            shutil.rmtree(temp_extract_dir)

                            # Remove the ZIP file
                            os.remove(zip_path)

                            # Log to CSV
                            log_to_csv(manufacturer, part_number, "SAMACSYS", new_name)

                            return new_name
                        else:
                            print(f"   ❌ No STEP files found in ZIP")
                            # Clean up temp directory
                            import shutil
                            shutil.rmtree(temp_extract_dir)

                except Exception as e:
                    print(f"   ❌ Error extracting ZIP: {e}")

            # Also check for direct STEP files (fallback)
            step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp'))]

            if step_files:
                step_file = step_files[0]
                print(f"   ✅ STEP file downloaded: {step_file}")

                # Move to 3d directory with proper naming
                src_path = os.path.join(downloads_dir, step_file)
                new_name = f"SamacSys-{manufacturer}-{part_number}.step"
                dst_path = os.path.join('3d', new_name)

                # Remove existing file if it exists
                if os.path.exists(dst_path):
                    os.remove(dst_path)
                    print(f"   🔍 Removed existing file: {new_name}")

                import shutil
                shutil.move(src_path, dst_path)
                print(f"   ✅ Moved to: 3d/{new_name}")

                # Log to CSV
                log_to_csv(manufacturer, part_number, "SAMACSYS", new_name)

                return new_name

            if i % 12 == 0:  # Every minute
                print(f"   ⏳ Still waiting for manual download... ({(i+1)*5}/300 seconds)")

        print(f"   ❌ No files downloaded after 5 minutes")
        return None

    def monitor_download_simple(self, driver, manufacturer, part_number, download_start_time):
        """Simple download monitoring after clicking download button once"""
        print(f"   🔸 Step 4: Monitoring for download...")
        downloads_dir = os.path.abspath('3d')

        # Check if directory exists
        if not os.path.exists(downloads_dir):
            print(f"   ❌ Directory doesn't exist: {downloads_dir}")
            return None

        for i in range(24):  # 2 minutes
            try:
                import time
                time.sleep(5)
                print(f"   ⏳ Checking... ({(i+1)*5}/120 seconds)")
            except Exception as e:
                print(f"   ❌ Exception in monitoring loop: {e}")
                continue

            # Check for files created after download started
            new_files = []

            if os.path.exists(downloads_dir):
                try:
                    all_files = os.listdir(downloads_dir)

                    for file in all_files:
                        file_path = os.path.join(downloads_dir, file)
                        if os.path.isfile(file_path) and not file.endswith('.crdownload'):
                            file_time = os.path.getmtime(file_path)
                            if file_time >= download_start_time:
                                new_files.append(file)
                                print(f"   📄 Found new file: {file}")
                except Exception as e:
                    print(f"   ❌ Error listing files: {e}")

            # Check for ZIP files first (SamacSys downloads ZIP files)
            zip_files = [f for f in new_files if f.lower().endswith('.zip')]

            if zip_files:
                zip_file = zip_files[0]
                print(f"   📦 ZIP file found: {zip_file}")

                # UNZIP THE STEP FILE
                zip_path = os.path.join(downloads_dir, zip_file)
                import zipfile

                # Wait a moment for browser to finish writing the file
                import time
                time.sleep(1)

                try:
                    # Open ZIP file explicitly to ensure proper closure
                    zip_ref = zipfile.ZipFile(zip_path, 'r')
                    try:
                        # Extract to temporary directory
                        temp_extract_dir = os.path.join('3d', 'temp_extract')
                        os.makedirs(temp_extract_dir, exist_ok=True)
                        zip_ref.extractall(temp_extract_dir)
                        print(f"   📂 Extracted ZIP contents")
                    finally:
                        # Explicitly close the ZIP file
                        zip_ref.close()
                        print(f"   🔒 Closed ZIP file")

                        # Find STEP files in extracted content
                        step_files = []
                        for root, dirs, files in os.walk(temp_extract_dir):
                            for file in files:
                                if file.lower().endswith(('.step', '.stp')):
                                    step_files.append(os.path.join(root, file))

                        if step_files:
                            # Use the first STEP file found
                            step_file_path = step_files[0]
                            step_file_name = os.path.basename(step_file_path)
                            print(f"   ✅ Found STEP file in ZIP: {step_file_name}")

                            # Move to 3d directory with proper naming
                            new_name = f"SamacSys-{manufacturer}-{part_number}.step"
                            dst_path = os.path.join('3d', new_name)

                            # Remove existing file if it exists
                            if os.path.exists(dst_path):
                                os.remove(dst_path)
                                print(f"   🔍 Removed existing file: {new_name}")

                            import shutil
                            shutil.move(step_file_path, dst_path)
                            print(f"   ✅ Moved STEP file to: 3d/{new_name}")

                            # Clean up temp directory
                            shutil.rmtree(temp_extract_dir)

                            # Log to CSV first
                            log_to_csv(manufacturer, part_number, "SAMACSYS", new_name)

                            # Close browser
                            driver.quit()

                        # ZIP file deletion OUTSIDE the with block - file is now closed
                        # Force garbage collection to release any cached file handles
                        import gc
                        gc.collect()

                        try:
                            os.remove(zip_path)
                            print(f"   🗑️ Removed ZIP file: {zip_file}")
                        except Exception as e:
                            print(f"   ❌ Could not remove ZIP file: {zip_file} - {e}")

                        return new_name

                except Exception as e:
                    print(f"   ❌ Error extracting ZIP: {e}")

            # Also check for direct STEP files (fallback)
            step_files = [f for f in new_files if f.lower().endswith(('.step', '.stp')) and not f.startswith('SamacSys-')]

            if step_files:
                step_file = step_files[0]
                print(f"   ✅ STEP file downloaded: {step_file}")

                # Move to 3d directory with proper naming
                src_path = os.path.join(downloads_dir, step_file)
                new_name = f"SamacSys-{manufacturer}-{part_number}.step"
                dst_path = os.path.join('3d', new_name)

                # Remove existing file if it exists
                if os.path.exists(dst_path):
                    os.remove(dst_path)
                    print(f"   🔍 Removed existing file: {new_name}")

                import shutil
                shutil.move(src_path, dst_path)
                print(f"   ✅ Moved to: 3d/{new_name}")

                # Log to CSV
                log_to_csv(manufacturer, part_number, "SAMACSYS", new_name)

                return new_name

        print(f"   ❌ No files downloaded after 2 minutes")
        return None

def find_3d_model(manufacturer, part_number, silent=False):
    """Main function to find 3D model"""
    if not silent:
        print("🎯 SAMACSYS 3D MODEL FINDER")
        print("=" * 50)
        print(f"🔍 Looking for: {manufacturer} {part_number}")

    finder = WorkingSamacSys()
    result = finder.search_and_download(manufacturer, part_number)

    if result:
        print(f"🎉 SUCCESS: {result} downloaded!")
        return result
    else:
        print(f"❌ FAILED: No 3D model obtained for {part_number}")
        return None

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python samacsys_3d_finder.py <manufacturer> <part_number>")
        print("Example: python samacsys_3d_finder.py \"Texas Instruments\" LM358N")
        print("Example: python samacsys_3d_finder.py \"Diodes Inc\" APX803L20-30SA-7")
        sys.exit(1)

    manufacturer = sys.argv[1]
    part_number = sys.argv[2]
    result = find_3d_model(manufacturer, part_number)
