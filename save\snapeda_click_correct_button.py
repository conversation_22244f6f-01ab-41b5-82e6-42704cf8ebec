#!/usr/bin/env python3
"""
SnapEDA Click Correct <PERSON><PERSON> - Click the actual visible download button
"""

import time
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

def click_correct_download_button():
    print("🔍 SNAPEDA CLICK CORRECT DOWNLOAD BUTTON")
    print("=" * 50)
    
    # Setup Chrome with download directory
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    # Set download directory to 3d folder
    download_dir = os.path.abspath('3d')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Quick login and navigation
        print("🔸 Logging in and navigating...")
        driver.get("https://www.snapeda.com/")
        time.sleep(5)
        
        # Click login
        login_link = driver.find_element(By.XPATH, "//a[contains(@href, 'login')]")
        login_link.click()
        time.sleep(5)
        
        # Enter part number and login
        search_field = driver.find_element(By.CSS_SELECTOR, "input[name='q']")
        search_field.send_keys("LM358N")
        
        email_field = driver.find_element(By.CSS_SELECTOR, "#id_username")
        email_field.send_keys("<EMAIL>")
        
        password_field = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
        password_field.send_keys("Lennyai123#")
        
        submit_btn = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
        submit_btn.click()
        time.sleep(10)
        
        # Go to part page
        part_url = "https://www.snapeda.com/parts/LM358N/Texas%20Instruments/view-part/"
        driver.get(part_url)
        time.sleep(10)
        
        # Click 3D Model tab
        three_d_tab = driver.find_element(By.XPATH, "//li[text()='3D Model']")
        three_d_tab.click()
        time.sleep(10)
        print("✅ 3D Model section opened")
        
        # List files before download
        print(f"\n📁 Files in 3d directory BEFORE download:")
        before_files = set(os.listdir(download_dir))
        for f in sorted(before_files):
            print(f"  - {f}")
        
        # Find and click the CORRECT download button
        print(f"\n🎯 Looking for the correct Download 3D Model button...")
        
        # Use the exact class we found
        download_button = driver.find_element(By.CSS_SELECTOR, "a.modal-trigger.3D-model-download")
        
        if download_button.is_displayed():
            print(f"✅ Found correct download button!")
            print(f"   Text: '{download_button.text.strip()}'")
            print(f"   Class: '{download_button.get_attribute('class')}'")
            print(f"   Displayed: {download_button.is_displayed()}")
            print(f"   Enabled: {download_button.is_enabled()}")
            
            # Take screenshot before click
            driver.save_screenshot("before_correct_click.png")
            print(f"📸 Screenshot saved: before_correct_click.png")
            
            # CLICK THE BUTTON
            print(f"\n🔸 CLICKING THE CORRECT DOWNLOAD BUTTON...")
            download_button.click()
            print(f"✅ CLICKED!")
            
            # Wait and take screenshot after click
            time.sleep(5)
            driver.save_screenshot("after_correct_click.png")
            print(f"📸 Screenshot saved: after_correct_click.png")
            
            # Check for changes
            new_url = driver.current_url
            new_title = driver.title
            print(f"📍 URL after click: {new_url}")
            print(f"📍 Title after click: {new_title}")
            
            # Monitor for file downloads
            print(f"\n📁 Monitoring for downloads...")
            for i in range(30):  # Wait up to 30 seconds
                time.sleep(1)
                current_files = set(os.listdir(download_dir))
                new_files = current_files - before_files
                
                if new_files:
                    print(f"✅ NEW FILES DETECTED after {i+1} seconds:")
                    for f in sorted(new_files):
                        file_path = os.path.join(download_dir, f)
                        file_size = os.path.getsize(file_path)
                        print(f"  - {f} ({file_size} bytes)")
                    break
                
                if i % 5 == 0:
                    print(f"   ⏳ Waiting... ({i+1}/30 seconds)")
            
            if not new_files:
                print(f"❌ No new files detected after 30 seconds")
                
                # List all files after waiting
                print(f"\n📁 Files in 3d directory AFTER download attempt:")
                after_files = set(os.listdir(download_dir))
                for f in sorted(after_files):
                    print(f"  - {f}")
        
        else:
            print(f"❌ Download button not displayed!")
        
        print(f"\n🔸 Test complete - browser staying open for inspection")
        print(f"🔸 Press Enter to close...")
        input()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        input()
    
    finally:
        driver.quit()

if __name__ == "__main__":
    click_correct_download_button()
