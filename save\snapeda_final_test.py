#!/usr/bin/env python3
"""
SnapEDA Final Test - Click the download button with correct selector
"""

import time
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

def final_download_test():
    print("🔍 SNAPEDA FINAL DOWNLOAD TEST")
    print("=" * 50)
    
    # Setup Chrome with download directory
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    download_dir = os.path.abspath('3d')
    os.makedirs(download_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Quick navigation to the 3D model section
        print("🔸 Quick navigation...")
        driver.get("https://www.snapeda.com/")
        time.sleep(5)
        
        login_link = driver.find_element(By.XPATH, "//a[contains(@href, 'login')]")
        login_link.click()
        time.sleep(5)
        
        search_field = driver.find_element(By.CSS_SELECTOR, "input[name='q']")
        search_field.send_keys("LM358N")
        
        email_field = driver.find_element(By.CSS_SELECTOR, "#id_username")
        email_field.send_keys("<EMAIL>")
        
        password_field = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
        password_field.send_keys("Lennyai123#")
        
        submit_btn = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
        submit_btn.click()
        time.sleep(10)
        
        part_url = "https://www.snapeda.com/parts/LM358N/Texas%20Instruments/view-part/"
        driver.get(part_url)
        time.sleep(10)
        
        three_d_tab = driver.find_element(By.XPATH, "//li[text()='3D Model']")
        three_d_tab.click()
        time.sleep(10)
        print("✅ Ready to test download")
        
        # List files before
        before_files = set(os.listdir(download_dir))
        print(f"\n📁 Files before: {len(before_files)} files")
        
        # Find download button using XPATH with the correct class
        print(f"\n🎯 Finding download button...")
        
        # Try multiple selectors for the download button
        selectors = [
            "//a[contains(@class, '3D-model-download')]",
            "//a[contains(@class, 'modal-trigger') and contains(text(), 'Download 3D Model')]",
            "//a[text()='Download 3D Model' and contains(@class, 'orange')]"
        ]
        
        download_button = None
        for selector in selectors:
            try:
                button = driver.find_element(By.XPATH, selector)
                if button.is_displayed():
                    download_button = button
                    print(f"✅ Found button with selector: {selector}")
                    break
            except:
                continue
        
        if download_button:
            print(f"✅ Download button found!")
            print(f"   Text: '{download_button.text.strip()}'")
            print(f"   Displayed: {download_button.is_displayed()}")
            print(f"   Enabled: {download_button.is_enabled()}")
            
            # CLICK THE BUTTON
            print(f"\n🔸 CLICKING DOWNLOAD BUTTON...")
            download_button.click()
            print(f"✅ CLICKED!")
            
            # Monitor for downloads
            print(f"\n📁 Monitoring downloads for 30 seconds...")
            for i in range(30):
                time.sleep(1)
                current_files = set(os.listdir(download_dir))
                new_files = current_files - before_files
                
                if new_files:
                    print(f"🎉 NEW FILE DOWNLOADED after {i+1} seconds!")
                    for f in sorted(new_files):
                        file_path = os.path.join(download_dir, f)
                        file_size = os.path.getsize(file_path)
                        print(f"  📄 {f} ({file_size} bytes)")
                    break
                
                if i % 5 == 0:
                    print(f"   ⏳ {i+1}/30 seconds...")
            
            if not new_files:
                print(f"❌ No download detected")
        else:
            print(f"❌ Download button not found!")
        
        print(f"\n🔸 Press Enter to close...")
        input()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        input()
    
    finally:
        driver.quit()

if __name__ == "__main__":
    final_download_test()
