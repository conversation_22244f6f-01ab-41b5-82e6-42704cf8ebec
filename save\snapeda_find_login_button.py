#!/usr/bin/env python3
"""
SnapEDA Find <PERSON><PERSON><PERSON> - Find the actual login button on the login form
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

def find_login_button():
    print("🔍 SNAPEDA FIND LOGIN BUTTON")
    print("=" * 50)
    
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Go to homepage and click login link
        print("🔸 Going to login page...")
        driver.get("https://www.snapeda.com/")
        time.sleep(5)
        
        login_link = driver.find_element(By.XPATH, "//a[contains(@href, 'login')]")
        login_link.click()
        time.sleep(5)
        print(f"✅ On login page: {driver.current_url}")
        
        # Fill the form first
        print("🔸 Filling login form...")
        
        search_field = driver.find_element(By.CSS_SELECTOR, "input[name='q']")
        search_field.send_keys("LM358N")
        print("✅ Entered part number")
        
        email_field = driver.find_element(By.CSS_SELECTOR, "#id_username")
        email_field.send_keys("<EMAIL>")
        print("✅ Entered email")
        
        password_field = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
        password_field.send_keys("Lennyai123#")
        print("✅ Entered password")
        
        # NOW FIND ALL POSSIBLE LOGIN BUTTONS
        print("\n🔍 SEARCHING FOR LOGIN BUTTONS...")
        
        # Method 1: All buttons
        print("\n📊 All buttons on the page:")
        all_buttons = driver.find_elements(By.TAG_NAME, "button")
        for i, btn in enumerate(all_buttons):
            try:
                text = btn.text.strip()
                btn_type = btn.get_attribute('type') or 'button'
                is_displayed = btn.is_displayed()
                is_enabled = btn.is_enabled()
                class_attr = btn.get_attribute('class') or 'no-class'
                
                if is_displayed:
                    print(f"  {i+1}. Button: '{text}' type='{btn_type}' enabled={is_enabled}")
                    print(f"      class='{class_attr[:50]}'")
            except:
                pass
        
        # Method 2: All input elements (including submit inputs)
        print("\n📊 All input elements:")
        all_inputs = driver.find_elements(By.TAG_NAME, "input")
        for i, inp in enumerate(all_inputs):
            try:
                input_type = inp.get_attribute('type') or 'text'
                input_value = inp.get_attribute('value') or 'no-value'
                is_displayed = inp.is_displayed()
                is_enabled = inp.is_enabled()
                class_attr = inp.get_attribute('class') or 'no-class'
                
                if is_displayed and input_type in ['submit', 'button']:
                    print(f"  {i+1}. Input: type='{input_type}' value='{input_value}' enabled={is_enabled}")
                    print(f"      class='{class_attr[:50]}'")
            except:
                pass
        
        # Method 3: Elements with login-related text
        print("\n📊 Elements with login-related text:")
        login_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'Log in') or contains(text(), 'Login') or contains(text(), 'Sign in') or contains(text(), 'Submit')]")
        for i, elem in enumerate(login_elements):
            try:
                text = elem.text.strip()
                tag = elem.tag_name
                is_displayed = elem.is_displayed()
                is_enabled = elem.is_enabled()
                class_attr = elem.get_attribute('class') or 'no-class'
                
                if is_displayed:
                    print(f"  {i+1}. <{tag}> '{text}' enabled={is_enabled}")
                    print(f"      class='{class_attr[:50]}'")
            except:
                pass
        
        # Method 4: Try to find by form submission
        print("\n📊 Forms on the page:")
        forms = driver.find_elements(By.TAG_NAME, "form")
        for i, form in enumerate(forms):
            try:
                action = form.get_attribute('action') or 'no-action'
                method = form.get_attribute('method') or 'no-method'
                print(f"  Form {i+1}: action='{action}' method='{method}'")
                
                # Find submit elements in this form
                form_buttons = form.find_elements(By.XPATH, ".//button | .//input[@type='submit']")
                for j, btn in enumerate(form_buttons):
                    if btn.is_displayed():
                        text = btn.text.strip() or btn.get_attribute('value') or 'no-text'
                        print(f"    Submit {j+1}: '{text}'")
            except:
                pass
        
        print(f"\n🔸 SEARCH COMPLETE")
        print(f"🔸 Browser staying open - you can see the login form")
        print(f"🔸 Press Enter to close...")
        input()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        input()
    
    finally:
        driver.quit()

if __name__ == "__main__":
    find_login_button()
