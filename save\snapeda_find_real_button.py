#!/usr/bin/env python3
"""
SnapEDA Find Real Button - Find the actual visible download button
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

def find_real_download_button():
    print("🔍 SNAPEDA FIND REAL DOWNLOAD BUTTON")
    print("=" * 50)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Quick login and navigation (same as before)
        driver.get("https://www.snapeda.com/")
        time.sleep(5)
        
        # Click login
        login_link = driver.find_element(By.XPATH, "//a[contains(@href, 'login')]")
        login_link.click()
        time.sleep(5)
        
        # Enter part number and login
        search_field = driver.find_element(By.CSS_SELECTOR, "input[name='q']")
        search_field.send_keys("LM358N")
        
        email_field = driver.find_element(By.CSS_SELECTOR, "#id_username")
        email_field.send_keys("<EMAIL>")
        
        password_field = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
        password_field.send_keys("Lennyai123#")
        
        submit_btn = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
        submit_btn.click()
        time.sleep(10)
        
        # Go to part page
        part_url = "https://www.snapeda.com/parts/LM358N/Texas%20Instruments/view-part/"
        driver.get(part_url)
        time.sleep(10)
        
        # Click 3D Model tab
        three_d_tab = driver.find_element(By.XPATH, "//li[text()='3D Model']")
        three_d_tab.click()
        time.sleep(10)
        
        print("✅ 3D Model section should now be open")
        
        # NOW SEARCH FOR ALL POSSIBLE DOWNLOAD BUTTONS
        print("\n🔍 SEARCHING FOR ALL DOWNLOAD-RELATED BUTTONS...")
        
        # Method 1: All buttons with "Download" in text
        print("\n📊 Method 1: Buttons with 'Download' in text:")
        download_text_buttons = driver.find_elements(By.XPATH, "//*[contains(text(), 'Download')]")
        for i, btn in enumerate(download_text_buttons):
            try:
                text = btn.text.strip()
                tag = btn.tag_name
                is_displayed = btn.is_displayed()
                is_enabled = btn.is_enabled()
                class_attr = btn.get_attribute('class') or 'no-class'
                
                if is_displayed:
                    print(f"  {i+1}. <{tag}> '{text}' displayed={is_displayed} enabled={is_enabled}")
                    print(f"      class='{class_attr[:60]}'")
            except:
                pass
        
        # Method 2: All orange buttons
        print("\n📊 Method 2: Orange buttons:")
        orange_buttons = driver.find_elements(By.XPATH, "//*[contains(@class, 'orange')]")
        for i, btn in enumerate(orange_buttons):
            try:
                text = btn.text.strip()
                tag = btn.tag_name
                is_displayed = btn.is_displayed()
                is_enabled = btn.is_enabled()
                class_attr = btn.get_attribute('class') or 'no-class'
                
                if is_displayed and text:
                    print(f"  {i+1}. <{tag}> '{text}' displayed={is_displayed} enabled={is_enabled}")
                    print(f"      class='{class_attr[:60]}'")
            except:
                pass
        
        # Method 3: All buttons in the 3D Model section
        print("\n📊 Method 3: All clickable elements (a, button, input):")
        all_clickable = driver.find_elements(By.XPATH, "//a | //button | //input[@type='submit']")
        displayed_count = 0
        
        for elem in all_clickable:
            try:
                if elem.is_displayed() and elem.is_enabled():
                    text = elem.text.strip()
                    tag = elem.tag_name
                    href = elem.get_attribute('href') or 'no-href'
                    class_attr = elem.get_attribute('class') or 'no-class'
                    
                    # Only show elements that might be download-related
                    if (text and ('download' in text.lower() or '3d' in text.lower() or 'model' in text.lower())) or \
                       ('download' in class_attr.lower()) or \
                       ('download' in href.lower()):
                        displayed_count += 1
                        print(f"  {displayed_count}. <{tag}> '{text}' href='{href[:30]}'")
                        print(f"      class='{class_attr[:60]}'")
            except:
                pass
        
        # Method 4: Try to find button by looking in specific 3D model container
        print("\n📊 Method 4: Looking in 3D model container:")
        try:
            # Look for common 3D model container classes
            containers = driver.find_elements(By.XPATH, "//*[contains(@class, '3d') or contains(@class, 'model') or contains(@class, 'viewer')]")
            for container in containers:
                if container.is_displayed():
                    buttons_in_container = container.find_elements(By.XPATH, ".//a | .//button")
                    for btn in buttons_in_container:
                        if btn.is_displayed():
                            text = btn.text.strip()
                            class_attr = btn.get_attribute('class') or 'no-class'
                            print(f"  Container button: '{text}' class='{class_attr[:60]}'")
        except:
            pass
        
        print(f"\n🔸 SEARCH COMPLETE - Browser staying open for inspection")
        print(f"🔸 Press Enter to close...")
        input()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        input()
    
    finally:
        driver.quit()

if __name__ == "__main__":
    find_real_download_button()
