#!/usr/bin/env python3
"""
SnapEDA Click Verification - Verify that we actually click the download button
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

def verify_download_click():
    print("🔍 SNAPEDA DOWNLOAD CLICK VERIFICATION")
    print("=" * 50)
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Step 1: Go to SnapEDA homepage
        print("🔸 Step 1: Going to SnapEDA homepage...")
        driver.get("https://www.snapeda.com/")
        time.sleep(8)
        print(f"✅ Homepage loaded: {driver.current_url}")
        
        # Step 2: Click login link
        print("🔸 Step 2: Looking for login link...")
        login_links = driver.find_elements(By.XPATH, "//a[contains(@href, 'login') or contains(text(), 'Log in') or contains(text(), 'Login')]")
        
        login_clicked = False
        for link in login_links:
            try:
                if link.is_displayed():
                    text = link.text.strip()
                    href = link.get_attribute('href') or ''
                    
                    if 'login' in href.lower() or any(word in text.lower() for word in ['log in', 'login', 'sign in']):
                        print(f"✅ Found login link: {text}")
                        link.click()
                        time.sleep(8)
                        login_clicked = True
                        break
            except:
                continue
        
        if not login_clicked:
            print("❌ Could not find login link")
            return
        
        print(f"✅ On login page: {driver.current_url}")
        
        # Step 3: Enter part number and login
        print("🔸 Step 3: Entering part number and logging in...")
        
        # Enter part number
        search_selectors = ["input[name='q']", "input[type='search']", "#search-input", ".search-input"]
        search_field = None
        for selector in search_selectors:
            try:
                search_field = driver.find_element(By.CSS_SELECTOR, selector)
                if search_field.is_displayed():
                    break
            except:
                continue
        
        if search_field:
            search_field.clear()
            search_field.send_keys("LM358N")
            print(f"✅ Entered part number")
        
        # Fill login form
        email = "<EMAIL>"
        password = "Lennyai123#"
        
        # Email field
        email_field = driver.find_element(By.CSS_SELECTOR, "#id_username")
        email_field.clear()
        email_field.send_keys(email)
        print("✅ Filled email")
        
        # Password field
        password_field = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
        password_field.clear()
        password_field.send_keys(password)
        print("✅ Filled password")
        
        # Submit button
        submit_btn = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
        submit_btn.click()
        print("✅ Clicked login")
        
        time.sleep(10)
        print(f"✅ Login completed: {driver.current_url}")
        
        # Step 4: Go to part page
        print("🔸 Step 4: Going to LM358N part page...")
        part_url = "https://www.snapeda.com/parts/LM358N/Texas%20Instruments/view-part/"
        driver.get(part_url)
        time.sleep(10)
        print(f"✅ On part page: {driver.current_url}")
        
        # Step 5: Click 3D Model tab
        print("🔸 Step 5: Looking for 3D Model tab...")
        three_d_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '3D')]")
        
        for elem in three_d_elements:
            try:
                if elem.is_displayed():
                    text = elem.text.strip()
                    if text == "3D Model":
                        print(f"✅ Found 3D Model tab: '{text}'")
                        elem.click()
                        print(f"✅ Clicked 3D Model tab")
                        time.sleep(10)
                        break
            except:
                continue
        
        # Step 6: VERIFY DOWNLOAD BUTTON EXISTS AND IS CLICKABLE
        print("🔸 Step 6: VERIFYING Download 3D Model button...")
        
        # Take screenshot before attempting click
        driver.save_screenshot("before_download_click.png")
        print("📸 Screenshot saved: before_download_click.png")
        
        # Show current page title and URL
        print(f"📍 Current page: {driver.title}")
        print(f"📍 Current URL: {driver.current_url}")
        
        # Look for Download 3D Model button with extensive debug
        print("🔍 Searching for Download 3D Model button...")
        
        # Method 1: Direct text search
        download_buttons = driver.find_elements(By.XPATH, "//a[contains(text(), 'Download 3D Model')]")
        print(f"📊 Found {len(download_buttons)} elements with 'Download 3D Model' text")
        
        for i, btn in enumerate(download_buttons):
            try:
                text = btn.text.strip()
                is_displayed = btn.is_displayed()
                is_enabled = btn.is_enabled()
                href = btn.get_attribute('href') or 'no-href'
                class_attr = btn.get_attribute('class') or 'no-class'
                
                print(f"  Button {i+1}: text='{text}' displayed={is_displayed} enabled={is_enabled}")
                print(f"             href='{href[:50]}' class='{class_attr[:50]}'")
                
                if is_displayed and is_enabled:
                    print(f"🎯 ATTEMPTING TO CLICK BUTTON {i+1}")
                    
                    # Try regular click first
                    try:
                        btn.click()
                        print(f"✅ REGULAR CLICK SUCCESSFUL!")
                        time.sleep(5)
                        
                        # Take screenshot after click
                        driver.save_screenshot("after_download_click.png")
                        print("📸 Screenshot saved: after_download_click.png")
                        
                        # Check if anything changed
                        new_url = driver.current_url
                        new_title = driver.title
                        print(f"📍 After click URL: {new_url}")
                        print(f"📍 After click title: {new_title}")
                        
                        break
                        
                    except Exception as e:
                        print(f"❌ Regular click failed: {e}")
                        
                        # Try JavaScript click
                        try:
                            driver.execute_script("arguments[0].click();", btn)
                            print(f"✅ JAVASCRIPT CLICK SUCCESSFUL!")
                            time.sleep(5)
                            
                            # Take screenshot after JS click
                            driver.save_screenshot("after_js_click.png")
                            print("📸 Screenshot saved: after_js_click.png")
                            
                            # Check if anything changed
                            new_url = driver.current_url
                            new_title = driver.title
                            print(f"📍 After JS click URL: {new_url}")
                            print(f"📍 After JS click title: {new_title}")
                            
                            break
                            
                        except Exception as e2:
                            print(f"❌ JavaScript click also failed: {e2}")
            except Exception as e:
                print(f"❌ Error checking button {i+1}: {e}")
        
        print(f"\n🔸 VERIFICATION COMPLETE")
        print(f"🔸 Browser will stay open for manual inspection")
        print(f"🔸 Press Enter to close browser...")
        
        # Wait for user input
        input()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print(f"🔸 Press Enter to close browser...")
        input()
    
    finally:
        driver.quit()

if __name__ == "__main__":
    verify_download_click()
