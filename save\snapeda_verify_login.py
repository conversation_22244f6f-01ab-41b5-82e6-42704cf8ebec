#!/usr/bin/env python3
"""
SnapEDA Verify Login - Check if login actually works
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

def verify_login():
    print("🔍 SNAPEDA LOGIN VERIFICATION")
    print("=" * 50)
    
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Step 1: Go to homepage
        print("🔸 Step 1: Going to SnapEDA homepage...")
        driver.get("https://www.snapeda.com/")
        time.sleep(5)
        print(f"✅ Homepage: {driver.current_url}")
        
        # Step 2: Click login
        print("🔸 Step 2: Clicking login link...")
        login_link = driver.find_element(By.XPATH, "//a[contains(@href, 'login')]")
        login_link.click()
        time.sleep(5)
        print(f"✅ Login page: {driver.current_url}")
        
        # Step 3: Fill login form
        print("🔸 Step 3: Filling login form...")
        
        # Enter part number first
        search_field = driver.find_element(By.CSS_SELECTOR, "input[name='q']")
        search_field.send_keys("LM358N")
        print("✅ Entered part number")
        
        # Enter email
        email_field = driver.find_element(By.CSS_SELECTOR, "#id_username")
        email_field.send_keys("<EMAIL>")
        print("✅ Entered email")
        
        # Enter password
        password_field = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
        password_field.send_keys("Lennyai123#")
        print("✅ Entered password")
        
        # Click submit
        submit_btn = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
        submit_btn.click()
        print("✅ Clicked submit")
        
        # Step 4: VERIFY LOGIN SUCCESS
        print("🔸 Step 4: Verifying login...")
        time.sleep(10)
        
        current_url = driver.current_url
        page_title = driver.title
        
        print(f"📍 After login URL: {current_url}")
        print(f"📍 After login title: {page_title}")
        
        # Check for login success indicators
        login_success = False
        
        # Method 1: Check if we're no longer on login page
        if "login" not in current_url.lower():
            print("✅ Not on login page anymore")
            login_success = True
        
        # Method 2: Look for user-specific elements
        try:
            user_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'Library') or contains(text(), 'Account') or contains(text(), 'Profile')]")
            if user_elements:
                print("✅ Found user-specific elements")
                login_success = True
        except:
            pass
        
        # Method 3: Check page content
        page_source = driver.page_source.lower()
        if "search" in current_url and "lm358n" in page_source:
            print("✅ On search results page with our part")
            login_success = True
        
        if login_success:
            print("🎉 LOGIN SUCCESSFUL!")
            
            # Now try to go to the specific part page
            print("🔸 Step 5: Going to LM358N part page...")
            part_url = "https://www.snapeda.com/parts/LM358N/Texas%20Instruments/view-part/"
            driver.get(part_url)
            time.sleep(10)
            
            final_url = driver.current_url
            final_title = driver.title
            
            print(f"📍 Part page URL: {final_url}")
            print(f"📍 Part page title: {final_title}")
            
            # Check if we're on the right part page
            if "LM358N" in final_title and "Texas Instruments" in final_title:
                print("🎉 ON CORRECT PART PAGE!")
            else:
                print("❌ Not on the expected part page")
        else:
            print("❌ LOGIN FAILED!")
            
            # Show what's on the page
            print("🔍 Current page elements:")
            try:
                error_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'error') or contains(text(), 'invalid') or contains(text(), 'incorrect')]")
                for elem in error_elements:
                    if elem.is_displayed():
                        print(f"  ⚠️ {elem.text.strip()}")
            except:
                pass
        
        print(f"\n🔸 Browser staying open for inspection")
        print(f"🔸 Press Enter to close...")
        input()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        input()
    
    finally:
        driver.quit()

if __name__ == "__main__":
    verify_login()
