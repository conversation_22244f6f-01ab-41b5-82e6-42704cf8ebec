#!/usr/bin/env python3
"""
3D STEP FILE FINDER - MODULAR VERSION
====================================
Extracted from the main component finder program.

Features:
- UltraLibrarian search (with Selenium support)
- SamacSys search (with Selenium support)  
- SnapEDA/SnapMagic search (with Selenium support)
- Manufacturer-specific searches (WURTH, Diodes Inc, I-PEX)
- Callable functions for integration

Usage:
    python 3d_finder.py "Texas Instruments" "LM358N"
"""

import requests
from bs4 import BeautifulSoup
import time
import os
import json
import argparse
from datetime import datetime
from urllib.parse import urljoin, urlparse, quote

class StepFileFinder:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive'
        })
        
        # Create directories
        os.makedirs('3d-models', exist_ok=True)
        
    def log(self, message, level="INFO"):
        """Enhanced logging with timestamps - fixed character duplication"""
        timestamp = datetime.now().strftime("%H:%M:%S")

        # Clean the message to prevent character duplication
        clean_message = str(message).strip()

        if level == "ERROR":
            print(f"❌ [{timestamp}] ERROR: {clean_message}")
        elif level == "SUCCESS":
            print(f"✅ [{timestamp}] SUCCESS: {clean_message}")
        elif level == "WARNING":
            print(f"⚠️ [{timestamp}] WARNING: {clean_message}")
        else:
            print(f"ℹ️ [{timestamp}] INFO: {clean_message}")
    
    def check_selenium_availability(self):
        """Check if Selenium is available"""
        try:
            from selenium import webdriver
            return True
        except ImportError:
            return False
    
    def search_ultralibrarian(self, manufacturer, part_number):
        """Search UltraLibrarian for STEP files"""
        try:
            self.log(f"🌐 Searching UltraLibrarian.com for {part_number}...")

            # Try Selenium first if available
            if self.check_selenium_availability():
                result = self._search_ultralibrarian_selenium(manufacturer, part_number)
                if result:
                    self.log(f"✅ 3D file found on UltraLibrarian.com", "SUCCESS")
                    return result

            # Fallback to requests-based search
            result = self._search_ultralibrarian_requests(manufacturer, part_number)
            if result:
                self.log(f"✅ 3D file found on UltraLibrarian.com", "SUCCESS")
                return result
            else:
                self.log(f"❌ 3D file not found on UltraLibrarian.com", "WARNING")
                return None

        except Exception as e:
            self.log(f"❌ 3D file not found on UltraLibrarian.com - error: {str(e)[:50]}", "ERROR")
            return None
    
    def _search_ultralibrarian_selenium(self, manufacturer, part_number):
        """Search UltraLibrarian using Selenium"""
        try:
            from selenium import webdriver
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.webdriver.chrome.options import Options
            
            self.log("Using Selenium for UltraLibrarian search...")
            
            # Setup Chrome options
            chrome_options = Options()
            chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            
            driver = webdriver.Chrome(options=chrome_options)
            
            try:
                search_url = f"https://www.ultralibrarian.com/search/{part_number}"
                driver.get(search_url)
                
                # Wait for search results
                wait = WebDriverWait(driver, 10)
                
                # Look for part results
                part_links = driver.find_elements(By.CSS_SELECTOR, 'a[href*="/part/"]')
                
                if part_links:
                    self.log(f"Found {len(part_links)} potential parts on UltraLibrarian")
                    
                    # Click on first result
                    part_links[0].click()
                    time.sleep(3)
                    
                    # Look for download links
                    download_links = driver.find_elements(By.CSS_SELECTOR, 'a[href*=".step"], a[href*=".stp"], a[download*=".step"]')
                    
                    if download_links:
                        download_url = download_links[0].get_attribute('href')
                        self.log(f"Found STEP download: {download_url[:50]}...")
                        
                        # Download the file
                        if self.download_step_file(download_url, manufacturer, part_number, "UltraLibrarian"):
                            filename = f"{manufacturer}_{part_number}_3d.step"
                            return (download_url, filename, "UltraLibrarian")
                
                return None
                
            finally:
                driver.quit()
                
        except Exception as e:
            self.log(f"Selenium UltraLibrarian error: {str(e)[:50]}", "ERROR")
            return None
    
    def _search_ultralibrarian_requests(self, manufacturer, part_number):
        """Fallback UltraLibrarian search using requests"""
        try:
            search_url = f"https://www.ultralibrarian.com/search/{part_number}"
            
            response = self.session.get(search_url, timeout=30)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Look for part links
                part_links = soup.find_all('a', href=lambda x: x and '/part/' in x)
                
                if part_links:
                    self.log(f"Found {len(part_links)} potential parts on UltraLibrarian")
                    
                    # Try first part link
                    part_url = urljoin(search_url, part_links[0]['href'])
                    part_response = self.session.get(part_url, timeout=30)
                    
                    if part_response.status_code == 200:
                        part_soup = BeautifulSoup(part_response.text, 'html.parser')
                        
                        # Look for STEP download links
                        step_links = part_soup.find_all('a', href=lambda x: x and ('.step' in x.lower() or '.stp' in x.lower()))
                        
                        if step_links:
                            download_url = urljoin(part_url, step_links[0]['href'])
                            self.log(f"Found STEP download: {download_url[:50]}...")
                            
                            if self.download_step_file(download_url, manufacturer, part_number, "UltraLibrarian"):
                                filename = f"{manufacturer}_{part_number}_3d.step"
                                return (download_url, filename, "UltraLibrarian")
            
            return None
            
        except Exception as e:
            self.log(f"Requests UltraLibrarian error: {str(e)[:50]}", "ERROR")
            return None
    
    def search_samacsys(self, manufacturer, part_number):
        """Search SamacSys for STEP files"""
        try:
            self.log(f"🌐 Searching SamacSys.com for {part_number}...")

            # Try Selenium first if available
            if self.check_selenium_availability():
                result = self._search_samacsys_selenium(manufacturer, part_number)
                if result:
                    self.log(f"✅ 3D file found on SamacSys.com", "SUCCESS")
                    return result

            # Fallback to requests-based search
            result = self._search_samacsys_requests(manufacturer, part_number)
            if result:
                self.log(f"✅ 3D file found on SamacSys.com", "SUCCESS")
                return result
            else:
                self.log(f"❌ 3D file not found on SamacSys.com", "WARNING")
                return None

        except Exception as e:
            self.log(f"❌ 3D file not found on SamacSys.com - error: {str(e)[:50]}", "ERROR")
            return None
    
    def _search_samacsys_selenium(self, manufacturer, part_number):
        """Search SamacSys using Selenium"""
        try:
            from selenium import webdriver
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.webdriver.chrome.options import Options
            
            self.log("Using Selenium for SamacSys search...")
            
            chrome_options = Options()
            chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            
            driver = webdriver.Chrome(options=chrome_options)
            
            try:
                search_url = f"https://componentsearchengine.com/search?term={part_number}"
                driver.get(search_url)
                
                wait = WebDriverWait(driver, 10)
                
                # Look for part results
                part_links = driver.find_elements(By.CSS_SELECTOR, 'a[href*="/part-view/"]')
                
                if part_links:
                    self.log(f"Found {len(part_links)} potential parts on SamacSys")
                    
                    part_links[0].click()
                    time.sleep(3)
                    
                    # Look for 3D model download
                    download_buttons = driver.find_elements(By.CSS_SELECTOR, 'button:contains("3D"), a:contains("STEP"), a:contains("3D")')
                    
                    if download_buttons:
                        download_buttons[0].click()
                        time.sleep(2)
                        
                        # Look for actual download link
                        step_links = driver.find_elements(By.CSS_SELECTOR, 'a[href*=".step"], a[href*=".stp"]')
                        
                        if step_links:
                            download_url = step_links[0].get_attribute('href')
                            self.log(f"Found STEP download: {download_url[:50]}...")
                            
                            if self.download_step_file(download_url, manufacturer, part_number, "SamacSys"):
                                filename = f"{manufacturer}_{part_number}_3d.step"
                                return (download_url, filename, "SamacSys")
                
                return None
                
            finally:
                driver.quit()
                
        except Exception as e:
            self.log(f"Selenium SamacSys error: {str(e)[:50]}", "ERROR")
            return None
    
    def _search_samacsys_requests(self, manufacturer, part_number):
        """Fallback SamacSys search using requests"""
        try:
            search_url = f"https://componentsearchengine.com/search?term={part_number}"
            
            response = self.session.get(search_url, timeout=30)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Look for part view links
                part_links = soup.find_all('a', href=lambda x: x and '/part-view/' in x)
                
                if part_links:
                    self.log(f"Found {len(part_links)} potential parts on SamacSys")
                    
                    part_url = urljoin(search_url, part_links[0]['href'])
                    part_response = self.session.get(part_url, timeout=30)
                    
                    if part_response.status_code == 200:
                        part_soup = BeautifulSoup(part_response.text, 'html.parser')
                        
                        # Look for STEP download links
                        step_links = part_soup.find_all('a', href=lambda x: x and ('.step' in x.lower() or '.stp' in x.lower()))
                        
                        if step_links:
                            download_url = urljoin(part_url, step_links[0]['href'])
                            self.log(f"Found STEP download: {download_url[:50]}...")
                            
                            if self.download_step_file(download_url, manufacturer, part_number, "SamacSys"):
                                filename = f"{manufacturer}_{part_number}_3d.step"
                                return (download_url, filename, "SamacSys")
            
            return None
            
        except Exception as e:
            self.log(f"Requests SamacSys error: {str(e)[:50]}", "ERROR")
            return None
    
    def download_step_file(self, url, manufacturer, part_number, source):
        """Download STEP file"""
        try:
            self.log(f"Downloading STEP file from {source}...")
            
            response = self.session.get(url, timeout=60, stream=True)
            
            if response.status_code == 200:
                # Generate filename
                clean_manufacturer = manufacturer.replace(' ', '_').replace('/', '_')
                clean_part = part_number.replace('/', '_').replace(' ', '_')
                filename = f"{clean_manufacturer}_{clean_part}_3d.step"
                filepath = os.path.join('3d-models', filename)
                
                # Download with progress
                total_size = int(response.headers.get('content-length', 0))
                downloaded_size = 0
                
                with open(filepath, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            downloaded_size += len(chunk)
                            
                            if total_size > 0:
                                progress = (downloaded_size / total_size) * 100
                                print(f"\r  📥 Progress: {progress:.1f}% ({downloaded_size:,} / {total_size:,} bytes)", end='')
                
                print()  # New line after progress
                
                # Verify file
                if os.path.exists(filepath) and os.path.getsize(filepath) > 0:
                    self.log(f"Downloaded: {filename} ({os.path.getsize(filepath):,} bytes)", "SUCCESS")
                    return True
                else:
                    self.log("Downloaded file is empty or invalid", "ERROR")
                    return False
            else:
                self.log(f"Download failed with status {response.status_code}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Download error: {str(e)[:50]}", "ERROR")
            return False

    def search_snapeda(self, manufacturer, part_number):
        """Search SnapEDA for STEP files"""
        try:
            # Try both SnapEDA and SnapMagic URLs
            search_urls = [
                f"https://www.snapeda.com/search?q={part_number}",
                f"https://www.snapmagic.com/search?q={part_number}"
            ]

            for search_url in search_urls:
                site_name = "SnapEDA" if "snapeda" in search_url else "SnapMagic"
                self.log(f"🌐 Searching {site_name}.com for {part_number}...")

                try:
                    response = self.session.get(search_url, timeout=30)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')

                        # Look for part links
                        part_links = soup.find_all('a', href=lambda x: x and ('/parts/' in x or '/part/' in x))

                        if part_links:
                            self.log(f"Found {len(part_links)} potential parts on {site_name}")

                            # Try first part
                            part_url = urljoin(search_url, part_links[0]['href'])
                            part_response = self.session.get(part_url, timeout=30)

                            if part_response.status_code == 200:
                                part_soup = BeautifulSoup(part_response.text, 'html.parser')

                                # Look for 3D model or STEP download links
                                step_links = part_soup.find_all('a', href=lambda x: x and ('.step' in x.lower() or '.stp' in x.lower() or '3d' in x.lower()))

                                if step_links:
                                    download_url = urljoin(part_url, step_links[0]['href'])
                                    self.log(f"Found STEP download: {download_url[:50]}...")

                                    if self.download_step_file(download_url, manufacturer, part_number, site_name):
                                        self.log(f"✅ 3D file found on {site_name}.com", "SUCCESS")
                                        filename = f"{manufacturer}_{part_number}_3d.step"
                                        return (download_url, filename, site_name)

                except Exception as e:
                    self.log(f"❌ 3D file not found on {site_name}.com - error: {str(e)[:30]}", "WARNING")
                    continue

            # If we get here, no files were found on any site
            self.log(f"❌ 3D file not found on SnapEDA.com or SnapMagic.com", "WARNING")
            return None

        except Exception as e:
            self.log(f"❌ 3D file not found on SnapEDA/SnapMagic - error: {str(e)[:50]}", "ERROR")
            return None

    def search_manufacturer_specific(self, manufacturer, part_number):
        """Search manufacturer-specific sources"""
        try:
            manufacturer_lower = manufacturer.lower()

            # WURTH Electronics
            if 'wurth' in manufacturer_lower or 'würth' in manufacturer_lower:
                self.log(f"🌐 Searching {manufacturer} website for {part_number}...")
                result = self.search_wurth(manufacturer, part_number)
                if result:
                    self.log(f"✅ 3D file found on {manufacturer} website", "SUCCESS")
                    return result
                else:
                    self.log(f"❌ 3D file not found on {manufacturer} website", "WARNING")
                    return None

            # Diodes Inc
            elif 'diodes' in manufacturer_lower:
                self.log(f"🌐 Searching {manufacturer} website for {part_number}...")
                result = self.search_diodes(manufacturer, part_number)
                if result:
                    self.log(f"✅ 3D file found on {manufacturer} website", "SUCCESS")
                    return result
                else:
                    self.log(f"❌ 3D file not found on {manufacturer} website", "WARNING")
                    return None

            # I-PEX/Hirose
            elif any(name in manufacturer_lower for name in ['i-pex', 'ipex', 'hirose']):
                self.log(f"🌐 Searching {manufacturer} website for {part_number}...")
                result = self.search_ipex(manufacturer, part_number)
                if result:
                    self.log(f"✅ 3D file found on {manufacturer} website", "SUCCESS")
                    return result
                else:
                    self.log(f"❌ 3D file not found on {manufacturer} website", "WARNING")
                    return None

            # Add more manufacturer-specific searches here
            else:
                self.log(f"🌐 No specific search method for {manufacturer} - skipping manufacturer website")
                return None

        except Exception as e:
            self.log(f"Manufacturer-specific search error: {str(e)[:50]}", "ERROR")
            return None

    def search_wurth(self, manufacturer, part_number):
        """Search WURTH Electronics website"""
        try:
            self.log(f"Searching WURTH for {part_number}...")

            search_url = f"https://www.we-online.com/en/components/products?sq={part_number}"
            response = self.session.get(search_url, timeout=30)

            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')

                # Look for product links
                product_links = soup.find_all('a', href=lambda x: x and '/product/' in x)

                if product_links:
                    self.log(f"Found {len(product_links)} potential products on WURTH")

                    # Try first product
                    product_url = urljoin(search_url, product_links[0]['href'])
                    product_response = self.session.get(product_url, timeout=30)

                    if product_response.status_code == 200:
                        product_soup = BeautifulSoup(product_response.text, 'html.parser')

                        # Look for 3D model downloads
                        download_links = product_soup.find_all('a', href=lambda x: x and ('.step' in x.lower() or '.stp' in x.lower() or '3d' in x.lower()))

                        if download_links:
                            download_url = urljoin(product_url, download_links[0]['href'])
                            self.log(f"Found STEP download: {download_url[:50]}...")

                            if self.download_step_file(download_url, manufacturer, part_number, "WURTH"):
                                filename = f"{manufacturer}_{part_number}_3d.step"
                                return (download_url, filename, "WURTH")

            return None

        except Exception as e:
            self.log(f"WURTH search error: {str(e)[:50]}", "ERROR")
            return None

    def search_diodes(self, manufacturer, part_number):
        """Search Diodes Inc website"""
        try:
            self.log(f"Searching Diodes Inc for {part_number}...")

            search_url = f"https://www.diodes.com/search/?q={part_number}"
            response = self.session.get(search_url, timeout=30)

            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')

                # Look for product links
                product_links = soup.find_all('a', href=lambda x: x and '/part/' in x)

                if product_links:
                    self.log(f"Found {len(product_links)} potential products on Diodes Inc")

                    # Try first product
                    product_url = urljoin(search_url, product_links[0]['href'])
                    product_response = self.session.get(product_url, timeout=30)

                    if product_response.status_code == 200:
                        product_soup = BeautifulSoup(product_response.text, 'html.parser')

                        # Look for 3D model downloads
                        download_links = product_soup.find_all('a', href=lambda x: x and ('.step' in x.lower() or '.stp' in x.lower() or '3d' in x.lower()))

                        if download_links:
                            download_url = urljoin(product_url, download_links[0]['href'])
                            self.log(f"Found STEP download: {download_url[:50]}...")

                            if self.download_step_file(download_url, manufacturer, part_number, "Diodes Inc"):
                                filename = f"{manufacturer}_{part_number}_3d.step"
                                return (download_url, filename, "Diodes Inc")

            return None

        except Exception as e:
            self.log(f"Diodes Inc search error: {str(e)[:50]}", "ERROR")
            return None

    def search_ipex(self, manufacturer, part_number):
        """Search I-PEX/Hirose website"""
        try:
            self.log(f"Searching I-PEX/Hirose for {part_number}...")

            # I-PEX connectors are made by Hirose
            search_urls = [
                f"https://www.hirose.com/en/products/search?keyword={part_number}",
                f"https://www.i-pex.com/search?q={part_number}"
            ]

            for search_url in search_urls:
                site_name = "Hirose" if "hirose" in search_url else "I-PEX"

                try:
                    response = self.session.get(search_url, timeout=30)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')

                        # Look for product links
                        product_links = soup.find_all('a', href=lambda x: x and ('/product/' in x or '/part/' in x))

                        if product_links:
                            self.log(f"Found {len(product_links)} potential products on {site_name}")

                            # Try first product
                            product_url = urljoin(search_url, product_links[0]['href'])
                            product_response = self.session.get(product_url, timeout=30)

                            if product_response.status_code == 200:
                                product_soup = BeautifulSoup(product_response.text, 'html.parser')

                                # Look for 3D model downloads
                                download_links = product_soup.find_all('a', href=lambda x: x and ('.step' in x.lower() or '.stp' in x.lower() or '3d' in x.lower()))

                                if download_links:
                                    download_url = urljoin(product_url, download_links[0]['href'])
                                    self.log(f"Found STEP download: {download_url[:50]}...")

                                    if self.download_step_file(download_url, manufacturer, part_number, site_name):
                                        filename = f"{manufacturer}_{part_number}_3d.step"
                                        return (download_url, filename, site_name)

                except Exception as e:
                    self.log(f"{site_name} error: {str(e)[:30]}", "WARNING")
                    continue

            return None

        except Exception as e:
            self.log(f"I-PEX/Hirose search error: {str(e)[:50]}", "ERROR")
            return None

def find_step_file(manufacturer, part_number, search_options=None):
    """
    Callable function to find and download STEP files

    Args:
        manufacturer (str): Manufacturer name (e.g., "Texas Instruments")
        part_number (str): Part number (e.g., "LM358N")
        search_options (dict): Optional dict with search options:
            - 'manufacturer': bool - Search manufacturer website
            - 'ultralibrarian': bool - Search UltraLibrarian
            - 'samacsys': bool - Search SamacSys
            - 'snapeda': bool - Search SnapEDA

    Returns:
        dict: {
            'success': bool,
            'message': str,
            'step_file': str or None,
            'source': str or None
        }
    """
    try:
        finder = StepFileFinder()

        # Set default options if not provided
        if search_options is None:
            search_options = {
                'manufacturer': True,
                'ultralibrarian': True,
                'samacsys': True,
                'snapeda': True
            }

        # Try UltraLibrarian first (if enabled)
        if search_options.get('ultralibrarian', True):
            finder.log("Step 1: Trying UltraLibrarian...")
            result = finder.search_ultralibrarian(manufacturer, part_number)
            if result:
                url, filename, source = result
                return {
                    'success': True,
                    'message': f"Found STEP file via {source}",
                    'step_file': f"3d-models/{filename}",
                    'source': source
                }
        else:
            finder.log("Step 1: UltraLibrarian search disabled - skipping")

        # Try SamacSys second (if enabled)
        if search_options.get('samacsys', True):
            finder.log("Step 2: Trying SamacSys...")
            result = finder.search_samacsys(manufacturer, part_number)
            if result:
                url, filename, source = result
                return {
                    'success': True,
                    'message': f"Found STEP file via {source}",
                    'step_file': f"3d-models/{filename}",
                    'source': source
                }
        else:
            finder.log("Step 2: SamacSys search disabled - skipping")

        # Try SnapEDA third (if enabled)
        if search_options.get('snapeda', True):
            finder.log("Step 3: Trying SnapEDA...")
            result = finder.search_snapeda(manufacturer, part_number)
            if result:
                url, filename, source = result
                return {
                    'success': True,
                    'message': f"Found STEP file via {source}",
                    'step_file': f"3d-models/{filename}",
                    'source': source
                }
        else:
            finder.log("Step 3: SnapEDA search disabled - skipping")

        # Try manufacturer-specific searches (if enabled)
        if search_options.get('manufacturer', True):
            finder.log("Step 4: Trying manufacturer-specific searches...")
            result = finder.search_manufacturer_specific(manufacturer, part_number)
            if result:
                url, filename, source = result
                return {
                    'success': True,
                    'message': f"Found STEP file via {source}",
                    'step_file': f"3d-models/{filename}",
                    'source': source
                }
        else:
            finder.log("Step 4: Manufacturer-specific search disabled - skipping")

        return {
            'success': False,
            'message': f"No STEP file found for {manufacturer} {part_number}",
            'step_file': None,
            'source': None
        }
        
    except Exception as e:
        return {
            'success': False,
            'message': f"Error: {str(e)}",
            'step_file': None,
            'source': None
        }

def main():
    parser = argparse.ArgumentParser(description='Find 3D STEP files for electronic components')
    parser.add_argument('manufacturer', help='Manufacturer name (e.g., "Texas Instruments")')
    parser.add_argument('part_number', help='Part number (e.g., "LM358N")')
    
    args = parser.parse_args()
    
    result = find_step_file(args.manufacturer, args.part_number)
    
    print(f"\n🎯 FINAL RESULTS")
    print("="*40)
    print(f"Success: {result['success']}")
    print(f"Message: {result['message']}")
    if result['step_file']:
        print(f"STEP File: {result['step_file']}")
        print(f"Source: {result['source']}")

if __name__ == "__main__":
    main()
