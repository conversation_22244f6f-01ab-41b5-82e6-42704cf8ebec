#!/usr/bin/env python3
"""
Direct API Test Program
Tests the APIs directly to identify the exact issues
"""

def test_digikey_api():
    """Test Digi-Key API directly"""
    print("Testing Digi-Key API...")
    
    try:
        from digikey_datasheet_improved import DigikeyDatasheetFinder
        
        # Create finder instance
        finder = DigikeyDatasheetFinder()
        
        # Load credentials
        if not finder.load_credentials():
            print("❌ Failed to load Digi-Key credentials")
            return False
            
        # Test search
        result = finder.search_part("Texas Instruments", "LM358N")
        
        if result:
            print("✅ Digi-Key API working")
            print(f"Result type: {type(result)}")
            if isinstance(result, dict):
                print(f"Result keys: {list(result.keys())}")
            return True
        else:
            print("❌ Digi-Key API returned None")
            return False
            
    except Exception as e:
        print(f"❌ Digi-Key API error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mouser_api():
    """Test Mouser API directly"""
    print("\nTesting Mouser API...")
    
    try:
        from mouser_datasheet_improved import MouserDatasheetFinder
        
        # Create finder instance
        finder = MouserDatasheetFinder()
        
        # Load credentials
        if not finder.load_credentials():
            print("❌ Failed to load Mouser credentials")
            return False
            
        # Test search
        result = finder.search_part("Texas Instruments", "LM358N")
        
        if result:
            print("✅ Mouser API working")
            print(f"Result type: {type(result)}")
            if isinstance(result, dict):
                print(f"Result keys: {list(result.keys())}")
            return True
        else:
            print("❌ Mouser API returned None")
            return False
            
    except Exception as e:
        print(f"❌ Mouser API error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_normal_searches():
    """Test normal web searches"""
    print("\nTesting normal web searches...")
    
    import requests
    
    # Test Digi-Key normal search
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
        
        url = "https://www.digikey.com/en/products/result?keywords=LM358N"
        response = requests.get(url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            if "lm358n" in response.text.lower():
                print("✅ Digi-Key normal search working")
            else:
                print("❌ Digi-Key normal search - part not found in response")
        else:
            print(f"❌ Digi-Key normal search failed: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ Digi-Key normal search error: {e}")
    
    # Test Mouser normal search
    try:
        url = "https://www.mouser.com/c/?q=LM358N"
        response = requests.get(url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            if "lm358n" in response.text.lower():
                print("✅ Mouser normal search working")
            else:
                print("❌ Mouser normal search - part not found in response")
        else:
            print(f"❌ Mouser normal search failed: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ Mouser normal search error: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("DIRECT API AND SEARCH TESTING")
    print("=" * 60)
    
    # Test APIs
    digikey_ok = test_digikey_api()
    mouser_ok = test_mouser_api()
    
    # Test normal searches
    test_normal_searches()
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    print(f"Digi-Key API: {'✅ Working' if digikey_ok else '❌ Failed'}")
    print(f"Mouser API: {'✅ Working' if mouser_ok else '❌ Failed'}")
    print("=" * 60)
