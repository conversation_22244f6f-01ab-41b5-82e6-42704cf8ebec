#!/usr/bin/env python3
"""
Simple test to check if the GUI starts correctly
"""

import sys
import os

print("🚀 Testing GUI startup...")
print(f"📁 Working directory: {os.getcwd()}")
print(f"🐍 Python version: {sys.version}")

try:
    print("📦 Importing tkinter...")
    import tkinter as tk
    from tkinter import ttk
    print("✅ Tkinter imported successfully")
    
    print("📦 Importing component_finder...")
    from component_finder import ComponentFinderGUI
    print("✅ ComponentFinderGUI imported successfully")
    
    print("🖥️ Creating root window...")
    root = tk.Tk()
    print("✅ Root window created")
    
    print("🔧 Creating ComponentFinderGUI...")
    app = ComponentFinderGUI(root)
    print("✅ ComponentFinderGUI created successfully")
    
    print("🎯 GUI should be visible now!")
    print("🔍 Look for window: 'Component Finder - Interactive Learning System'")
    
    # Start the GUI
    root.mainloop()
    print("🏁 GUI closed")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    input("Press Enter to exit...")
